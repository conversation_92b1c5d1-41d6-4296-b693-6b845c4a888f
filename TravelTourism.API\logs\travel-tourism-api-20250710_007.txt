2025-07-10 23:22:23.469 +03:00 [WRN] Entity 'Blog' has a global query filter defined and is the required end of a relationship with the entity 'BlogImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-10 23:22:24.266 +03:00 [WRN] Entity 'Booking' has a global query filter defined and is the required end of a relationship with the entity 'BookingPayment'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-10 23:22:24.422 +03:00 [WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-10 23:22:24.433 +03:00 [WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripItinerary'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-10 23:22:24.438 +03:00 [WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
2025-07-10 23:22:24.715 +03:00 [WRN] The 'UserRole' property 'Role' on entity type 'User' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'UserRole' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
2025-07-10 23:22:27.571 +03:00 [WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Currency'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
2025-07-10 23:22:36.120 +03:00 [INF] Starting Travel & Tourism API
2025-07-10 23:22:43.343 +03:00 [INF] Request GET /swagger started at "2025-07-10T20:22:43.3369045Z"
2025-07-10 23:22:43.783 +03:00 [INF] Request GET /swagger completed with status 404 in 446ms
2025-07-10 23:22:48.005 +03:00 [INF] Request OPTIONS /api/v1/trips/categories started at "2025-07-10T20:22:48.0051695Z"
2025-07-10 23:22:48.005 +03:00 [INF] Request OPTIONS /api/v1/trips started at "2025-07-10T20:22:48.0051695Z"
2025-07-10 23:22:48.034 +03:00 [INF] Request OPTIONS /api/v1/trips/categories completed with status 204 in 29ms
2025-07-10 23:22:48.034 +03:00 [INF] Request OPTIONS /api/v1/trips completed with status 204 in 29ms
2025-07-10 23:22:48.105 +03:00 [INF] Request GET /api/v1/trips/categories started at "2025-07-10T20:22:48.1049925Z"
2025-07-10 23:22:48.115 +03:00 [INF] Request GET /api/v1/trips started at "2025-07-10T20:22:48.1152403Z"
2025-07-10 23:22:53.145 +03:00 [INF] Request GET /api/v1/trips/categories completed with status 200 in 5040ms
2025-07-10 23:22:53.525 +03:00 [INF] Request GET /api/v1/trips completed with status 200 in 5410ms
2025-07-10 23:23:01.902 +03:00 [INF] Request OPTIONS /api/v1/blogs/featured started at "2025-07-10T20:23:01.9023973Z"
2025-07-10 23:23:01.811 +03:00 [INF] Request OPTIONS /api/v1/trips/featured started at "2025-07-10T20:23:01.7980515Z"
2025-07-10 23:23:02.144 +03:00 [INF] Request OPTIONS /api/v1/blogs/featured completed with status 204 in 241ms
2025-07-10 23:23:02.147 +03:00 [INF] Request OPTIONS /api/v1/trips/featured completed with status 204 in 349ms
2025-07-10 23:23:02.257 +03:00 [INF] Request GET /api/v1/trips/featured started at "2025-07-10T20:23:02.2572399Z"
2025-07-10 23:23:02.258 +03:00 [INF] Request GET /api/v1/blogs/featured started at "2025-07-10T20:23:02.2582144Z"
2025-07-10 23:23:02.520 +03:00 [INF] Request GET /api/v1/trips/featured completed with status 200 in 263ms
2025-07-10 23:23:02.757 +03:00 [INF] Request GET /api/v1/blogs/featured completed with status 200 in 498ms
2025-07-10 23:23:05.731 +03:00 [INF] Request OPTIONS /api/v1/trips/categories started at "2025-07-10T20:23:05.7310719Z"
2025-07-10 23:23:05.811 +03:00 [INF] Request OPTIONS /api/v1/trips/categories completed with status 204 in 80ms
2025-07-10 23:23:05.731 +03:00 [INF] Request OPTIONS /api/v1/trips started at "2025-07-10T20:23:05.7311321Z"
2025-07-10 23:23:05.845 +03:00 [INF] Request OPTIONS /api/v1/trips completed with status 204 in 114ms
2025-07-10 23:23:05.944 +03:00 [INF] Request GET /api/v1/trips started at "2025-07-10T20:23:05.9440670Z"
2025-07-10 23:23:05.915 +03:00 [INF] Request GET /api/v1/trips/categories started at "2025-07-10T20:23:05.9156881Z"
2025-07-10 23:23:06.045 +03:00 [INF] Request GET /api/v1/trips completed with status 200 in 101ms
2025-07-10 23:23:06.040 +03:00 [INF] Request GET /api/v1/trips/categories completed with status 200 in 124ms
2025-07-10 23:23:09.822 +03:00 [INF] Request OPTIONS /api/v1/blogs/categories started at "2025-07-10T20:23:09.8228839Z"
2025-07-10 23:23:09.825 +03:00 [INF] Request OPTIONS /api/v1/blogs started at "2025-07-10T20:23:09.8251236Z"
2025-07-10 23:23:09.823 +03:00 [INF] Request OPTIONS /api/v1/blogs/featured started at "2025-07-10T20:23:09.8236901Z"
2025-07-10 23:23:09.899 +03:00 [INF] Request OPTIONS /api/v1/blogs/categories completed with status 204 in 76ms
2025-07-10 23:23:09.926 +03:00 [INF] Request OPTIONS /api/v1/blogs completed with status 204 in 101ms
2025-07-10 23:23:09.931 +03:00 [INF] Request OPTIONS /api/v1/blogs/featured completed with status 204 in 107ms
2025-07-10 23:23:09.997 +03:00 [INF] Request GET /api/v1/blogs started at "2025-07-10T20:23:09.9974826Z"
2025-07-10 23:23:09.997 +03:00 [INF] Request GET /api/v1/blogs/categories started at "2025-07-10T20:23:09.9974631Z"
2025-07-10 23:23:10.224 +03:00 [INF] Request GET /api/v1/blogs/categories completed with status 200 in 226ms
2025-07-10 23:23:10.175 +03:00 [INF] Request GET /api/v1/blogs/featured started at "2025-07-10T20:23:10.1748971Z"
2025-07-10 23:23:10.213 +03:00 [INF] Request GET /api/v1/blogs completed with status 200 in 215ms
2025-07-10 23:23:10.330 +03:00 [INF] Request GET /api/v1/blogs/featured completed with status 200 in 141ms
2025-07-10 23:23:10.933 +03:00 [INF] Request GET /api/v1/trips/categories started at "2025-07-10T20:23:10.9330163Z"
2025-07-10 23:23:10.933 +03:00 [INF] Request GET /api/v1/trips started at "2025-07-10T20:23:10.9330163Z"
2025-07-10 23:23:11.075 +03:00 [INF] Request GET /api/v1/trips/categories completed with status 200 in 142ms
2025-07-10 23:23:11.091 +03:00 [INF] Request GET /api/v1/trips completed with status 200 in 158ms
2025-07-10 23:23:11.446 +03:00 [INF] Request OPTIONS /api/v1/trips/featured started at "2025-07-10T20:23:11.4469045Z"
2025-07-10 23:23:11.461 +03:00 [INF] Request GET /api/v1/blogs/featured started at "2025-07-10T20:23:11.4610349Z"
2025-07-10 23:23:12.089 +03:00 [INF] Request OPTIONS /api/v1/trips/featured completed with status 204 in 643ms
2025-07-10 23:23:12.106 +03:00 [INF] Request GET /api/v1/blogs/featured completed with status 200 in 645ms
2025-07-10 23:23:12.176 +03:00 [INF] Request GET /api/v1/trips/featured started at "2025-07-10T20:23:12.1764416Z"
2025-07-10 23:23:12.213 +03:00 [INF] Request GET /api/v1/trips/featured completed with status 200 in 37ms
2025-07-10 23:23:52.469 +03:00 [INF] Request OPTIONS /api/v1/auth/register started at "2025-07-10T20:23:52.4692378Z"
2025-07-10 23:23:52.492 +03:00 [INF] Request OPTIONS /api/v1/auth/register completed with status 204 in 23ms
2025-07-10 23:23:52.509 +03:00 [INF] Request POST /api/v1/auth/register started at "2025-07-10T20:23:52.5098731Z"
2025-07-10 23:23:54.313 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'TravelTourism.Infrastructure.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'AddressLine1', table 'TravelTourismDB.dbo.Users'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:1073b0bb-fcef-4d74-ae17-6349f78a35c9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'AddressLine1', table 'TravelTourismDB.dbo.Users'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:1073b0bb-fcef-4d74-ae17-6349f78a35c9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-10 23:23:54.973 +03:00 [INF] Request POST /api/v1/auth/register completed with status 400 in 2463ms
2025-07-10 23:24:13.440 +03:00 [INF] Request OPTIONS /api/v1/auth/register started at "2025-07-10T20:24:13.4400726Z"
2025-07-10 23:24:13.455 +03:00 [INF] Request OPTIONS /api/v1/auth/register completed with status 204 in 15ms
2025-07-10 23:24:13.469 +03:00 [INF] Request POST /api/v1/auth/register started at "2025-07-10T20:24:13.4695030Z"
2025-07-10 23:24:13.829 +03:00 [ERR] An exception occurred in the database while saving changes for context type 'TravelTourism.Infrastructure.Data.ApplicationDbContext'.
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'AddressLine1', table 'TravelTourismDB.dbo.Users'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:1073b0bb-fcef-4d74-ae17-6349f78a35c9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
Microsoft.EntityFrameworkCore.DbUpdateException: An error occurred while saving the entity changes. See the inner exception for details.
 ---> Microsoft.Data.SqlClient.SqlException (0x80131904): Cannot insert the value NULL into column 'AddressLine1', table 'TravelTourismDB.dbo.Users'; column does not allow nulls. INSERT fails.
   at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryHasMoreRows(Boolean& moreRows)
   at Microsoft.Data.SqlClient.SqlDataReader.TryReadInternal(Boolean setTimeout, Boolean& more)
   at Microsoft.Data.SqlClient.SqlDataReader.ReadAsyncExecute(Task task, Object state)
   at Microsoft.Data.SqlClient.SqlDataReader.InvokeAsyncCall[T](SqlDataReaderBaseAsyncCallContext`1 context)
--- End of stack trace from previous location ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
ClientConnectionId:1073b0bb-fcef-4d74-ae17-6349f78a35c9
Error Number:515,State:2,Class:16
   --- End of inner exception stack trace ---
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeResultSetAsync(Int32 startCommandIndex, RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.AffectedCountModificationCommandBatch.ConsumeAsync(RelationalDataReader reader, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.ReaderModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Update.Internal.SqlServerModificationCommandBatch.ExecuteAsync(IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Update.Internal.BatchExecutor.ExecuteAsync(IEnumerable`1 commandBatches, IRelationalConnection connection, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.Storage.RelationalDatabase.SaveChangesAsync(IList`1 entries, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.SqlServer.Storage.Internal.SqlServerExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
2025-07-10 23:24:14.402 +03:00 [INF] Request POST /api/v1/auth/register completed with status 400 in 933ms
[2025-07-10 23:36:50.463 +03:00 WRN] Entity 'Blog' has a global query filter defined and is the required end of a relationship with the entity 'BlogImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-10 23:36:50.560 +03:00 WRN] Entity 'Booking' has a global query filter defined and is the required end of a relationship with the entity 'BookingPayment'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-10 23:36:50.565 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-10 23:36:50.570 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripItinerary'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-10 23:36:50.578 +03:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-10 23:36:50.732 +03:00 WRN] The 'UserRole' property 'Role' on entity type 'User' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'UserRole' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-07-10 23:36:50.747 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Currency'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
[2025-07-10 23:36:52.930 +03:00 INF] Starting Travel & Tourism API
