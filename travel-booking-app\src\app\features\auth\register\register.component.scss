// Additional styles specific to register component
.auth-card {
  max-width: 600px; // Wider for multi-step form
  min-height: 600px; // Consistent height
}

// Progress Steps
.progress-steps {
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 30px 0;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 20%;
    right: 20%;
    height: 2px;
    background: #cbd5e0;
    z-index: 1;
  }

  .step {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    z-index: 2;
    background: white;
    padding: 0 15px;
    transition: all 0.3s ease;

    .step-number {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: #e2e8f0;
      color: #4a5568;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 700;
      margin-bottom: 8px;
      transition: all 0.3s ease;
      border: 2px solid #e2e8f0;
    }

    span {
      font-size: 13px;
      color: #4a5568;
      font-weight: 600;
      transition: color 0.3s ease;
    }

    &.active {
      .step-number {
        background: #667eea;
        color: white;
        transform: scale(1.1);
        border-color: #667eea;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
      }

      span {
        color: #667eea;
        font-weight: 700;
      }
    }

    &.completed {
      .step-number {
        background: #38a169;
        color: white;
        border-color: #38a169;
      }

      span {
        color: #38a169;
        font-weight: 600;
      }
    }
  }
}

// Step Content
.step-content {
  min-height: 400px;

  h3 {
    color: #2d3748;
    margin-bottom: 25px;
    font-size: 1.4rem;
    font-weight: 700;
    text-align: center;
  }
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;

  @media (max-width: 480px) {
    grid-template-columns: 1fr;
    gap: 0;
  }

  .form-group {
    margin-bottom: 0;

    @media (max-width: 480px) {
      margin-bottom: 20px;
    }
  }
}

// Enhanced form styling
.form-group {
  label {
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 8px;
    display: block;
    font-size: 0.9rem;

    &::after {
      content: ' *';
      color: #e53e3e;
      display: none;
    }
  }

  input, select {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;

    &:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    &.error {
      border-color: #e53e3e;
      background: #fed7d7;
      box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
    }

    &::placeholder {
      color: #a0aec0;
    }
  }

  select {
    cursor: pointer;

    option {
      padding: 10px;
    }
  }

  .error-message {
    color: #e53e3e;
    font-size: 0.8rem;
    margin-top: 5px;
    font-weight: 500;
  }
}

// Navigation buttons
.form-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 30px;
  gap: 15px;

  .btn {
    flex: 1;
    max-width: 200px;
    padding: 14px 24px;
    font-size: 1rem;
    font-weight: 600;
    border-radius: 10px;
    transition: all 0.3s ease;
    cursor: pointer;
    border: none;

    &.btn-secondary {
      background: #4a5568;
      color: white;
      border: 2px solid #4a5568;

      &:hover:not(:disabled) {
        background: #2d3748;
        border-color: #2d3748;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(74, 85, 104, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }

    &.btn-primary {
      background: #667eea;
      color: white;

      &:hover:not(:disabled) {
        background: #5a67d8;
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }
  }
}

// Password strength indicator
.password-strength {
  margin-top: 8px;

  .strength-bar {
    height: 4px;
    background: #e0e0e0;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 5px;

    .strength-fill {
      height: 100%;
      transition: all 0.3s ease;
      border-radius: 2px;

      &.weak {
        width: 33%;
        background: var(--error-color);
      }

      &.medium {
        width: 66%;
        background: var(--warning-color);
      }

      &.strong {
        width: 100%;
        background: var(--success-color);
      }
    }
  }

  .strength-text {
    font-size: 12px;
    font-weight: 500;

    &.weak { color: var(--error-color); }
    &.medium { color: var(--warning-color); }
    &.strong { color: var(--success-color); }
  }
}

// Responsive design
@media (max-width: 768px) {
  .progress-steps {
    margin: 20px 0;

    .step {
      padding: 0 10px;

      .step-number {
        width: 35px;
        height: 35px;
        font-size: 14px;
      }

      span {
        font-size: 11px;
      }
    }
  }

  .step-content h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .form-navigation {
    flex-direction: column;

    .btn {
      max-width: none;
      width: 100%;
    }
  }
}

.password-strength {
  margin-top: 8px;
  display: flex;
  align-items: center;
  gap: 10px;

  .strength-bar {
    flex: 1;
    height: 4px;
    border-radius: 2px;
    background: #e2e8f0;
    position: relative;
    overflow: hidden;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      border-radius: 2px;
      transition: all 0.3s ease;
    }

    &.weak::after {
      width: 33%;
      background: #e53e3e;
    }

    &.medium::after {
      width: 66%;
      background: #f6ad55;
    }

    &.strong::after {
      width: 100%;
      background: #38a169;
    }
  }

  .strength-text {
    font-size: 0.8rem;
    font-weight: 500;
    min-width: 60px;

    &.weak {
      color: #e53e3e;
    }

    &.medium {
      color: #f6ad55;
    }

    &.strong {
      color: #38a169;
    }
  }
}

.checkbox-label {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #4a5568;
  display: flex;
  align-items: flex-start;
  cursor: pointer;
  margin-bottom: 15px;

  input[type="checkbox"] {
    display: none;
  }

  .checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #e2e8f0;
    border-radius: 4px;
    margin-right: 12px;
    margin-top: 2px;
    position: relative;
    transition: all 0.3s ease;
    flex-shrink: 0;

    &::after {
      content: '';
      position: absolute;
      left: 5px;
      top: 2px;
      width: 4px;
      height: 8px;
      border: solid white;
      border-width: 0 2px 2px 0;
      transform: rotate(45deg);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
  }

  input[type="checkbox"]:checked + .checkmark {
    background: #667eea;
    border-color: #667eea;

    &::after {
      opacity: 1;
    }
  }

  a {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;

    &:hover {
      text-decoration: underline;
    }
  }
}

// Override some styles for better spacing in register form
.form-group {
  margin-bottom: 20px;

  &:last-of-type {
    margin-bottom: 25px;
  }
}

.auth-form {
  .form-group:nth-last-child(3) {
    margin-bottom: 15px; // Terms checkbox
  }

  .form-group:nth-last-child(2) {
    margin-bottom: 25px; // Newsletter checkbox
  }
}

// Alert styling
.alert {
  padding: 12px 16px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-size: 0.9rem;
  font-weight: 500;

  &.alert-error {
    background: #fed7d7;
    color: #c53030;
    border: 1px solid #feb2b2;
  }
}

// Spinner animation
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: white;
  animation: spin 1s ease-in-out infinite;
  margin-right: 8px;
}

// Password input styling
.password-input {
  position: relative;

  .password-toggle {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    color: #718096;
    padding: 4px;
    z-index: 10;

    &:hover {
      color: #4a5568;
    }
  }
}
