import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { FormBuilder, FormGroup, Validators, ReactiveFormsModule, AbstractControl } from '@angular/forms';
import { firstValueFrom } from 'rxjs';
import { AuthService, NotificationService } from '../../../core/services';
import { trigger, transition, style, animate } from '@angular/animations';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, RouterModule, ReactiveFormsModule],
  animations: [
    trigger('slideIn', [
      transition(':enter', [
        style({ opacity: 0, transform: 'translateX(50px)' }),
        animate('300ms ease-in', style({ opacity: 1, transform: 'translateX(0)' }))
      ])
    ])
  ],
  template: `
    <div class="auth-container">
      <div class="auth-card">
        <div class="auth-header">
          <h1>Create Account</h1>
          <p>Join us and start your travel adventure today</p>
          
          <!-- Progress Steps -->
          <div class="progress-steps">
            <div class="step" [class.active]="currentStep() >= 1" [class.completed]="currentStep() > 1">
              <div class="step-number">1</div>
              <span>Personal Info</span>
            </div>
            <div class="step" [class.active]="currentStep() >= 2" [class.completed]="currentStep() > 2">
              <div class="step-number">2</div>
              <span>Address</span>
            </div>
            <div class="step" [class.active]="currentStep() >= 3" [class.completed]="currentStep() > 3">
              <div class="step-number">3</div>
              <span>Security</span>
            </div>
          </div>
        </div>

        <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="auth-form">
          
          <!-- Step 1: Personal Information -->
          @if (currentStep() === 1) {
            <div class="step-content" [@slideIn]>
              <h3>Personal Information</h3>
              
              <div class="form-row">
                <div class="form-group">
                  <label for="firstName">First Name *</label>
                  <input
                    id="firstName"
                    type="text"
                    formControlName="firstName"
                    placeholder="Enter your first name"
                    [class.error]="isFieldInvalid('firstName')"
                  />
                  @if (isFieldInvalid('firstName')) {
                    <div class="error-message">First name is required</div>
                  }
                </div>

                <div class="form-group">
                  <label for="lastName">Last Name *</label>
                  <input
                    id="lastName"
                    type="text"
                    formControlName="lastName"
                    placeholder="Enter your last name"
                    [class.error]="isFieldInvalid('lastName')"
                  />
                  @if (isFieldInvalid('lastName')) {
                    <div class="error-message">Last name is required</div>
                  }
                </div>
              </div>

              <div class="form-group">
                <label for="email">Email Address *</label>
                <input
                  id="email"
                  type="email"
                  formControlName="email"
                  placeholder="Enter your email"
                  [class.error]="isFieldInvalid('email')"
                />
                @if (isFieldInvalid('email')) {
                  <div class="error-message">
                    @if (registerForm.get('email')?.errors?.['required']) {
                      Email is required
                    } @else if (registerForm.get('email')?.errors?.['email']) {
                      Please enter a valid email address
                    }
                  </div>
                }
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="phoneNumber">Phone Number</label>
                  <input
                    id="phoneNumber"
                    type="tel"
                    formControlName="phoneNumber"
                    placeholder="Enter your phone number"
                  />
                </div>

                <div class="form-group">
                  <label for="dateOfBirth">Date of Birth</label>
                  <input
                    id="dateOfBirth"
                    type="date"
                    formControlName="dateOfBirth"
                  />
                </div>
              </div>

              <div class="form-group">
                <label for="gender">Gender</label>
                <select id="gender" formControlName="gender">
                  <option value="">Select Gender</option>
                  <option value="1">Male</option>
                  <option value="2">Female</option>
                  <option value="3">Other</option>
                </select>
              </div>
            </div>
          }

          <!-- Step 2: Address Information -->
          @if (currentStep() === 2) {
            <div class="step-content" [@slideIn]>
              <h3>Address Information</h3>
              
              <div class="form-group">
                <label for="addressLine1">Address Line 1 *</label>
                <input
                  id="addressLine1"
                  type="text"
                  formControlName="addressLine1"
                  placeholder="Street address, P.O. box, company name"
                  [class.error]="isFieldInvalid('addressLine1')"
                />
                @if (isFieldInvalid('addressLine1')) {
                  <div class="error-message">Address Line 1 is required</div>
                }
              </div>

              <div class="form-group">
                <label for="addressLine2">Address Line 2 *</label>
                <input
                  id="addressLine2"
                  type="text"
                  formControlName="addressLine2"
                  placeholder="Apartment, suite, unit, building, floor, etc."
                  [class.error]="isFieldInvalid('addressLine2')"
                />
                @if (isFieldInvalid('addressLine2')) {
                  <div class="error-message">Address Line 2 is required</div>
                }
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="city">City *</label>
                  <input
                    id="city"
                    type="text"
                    formControlName="city"
                    placeholder="Enter your city"
                    [class.error]="isFieldInvalid('city')"
                  />
                  @if (isFieldInvalid('city')) {
                    <div class="error-message">City is required</div>
                  }
                </div>

                <div class="form-group">
                  <label for="state">State/Province *</label>
                  <input
                    id="state"
                    type="text"
                    formControlName="state"
                    placeholder="Enter your state"
                    [class.error]="isFieldInvalid('state')"
                  />
                  @if (isFieldInvalid('state')) {
                    <div class="error-message">State is required</div>
                  }
                </div>
              </div>

              <div class="form-row">
                <div class="form-group">
                  <label for="country">Country *</label>
                  <input
                    id="country"
                    type="text"
                    formControlName="country"
                    placeholder="Enter your country"
                    [class.error]="isFieldInvalid('country')"
                  />
                  @if (isFieldInvalid('country')) {
                    <div class="error-message">Country is required</div>
                  }
                </div>

                <div class="form-group">
                  <label for="postalCode">Postal Code *</label>
                  <input
                    id="postalCode"
                    type="text"
                    formControlName="postalCode"
                    placeholder="Enter postal code"
                    [class.error]="isFieldInvalid('postalCode')"
                  />
                  @if (isFieldInvalid('postalCode')) {
                    <div class="error-message">Postal code is required</div>
                  }
                </div>
              </div>
            </div>
          }

          <!-- Step 3: Security -->
          @if (currentStep() === 3) {
            <div class="step-content" [@slideIn]>
              <h3>Security & Preferences</h3>
              
              <div class="form-group">
                <label for="password">Password *</label>
                <div class="password-input">
                  <input
                    id="password"
                    [type]="showPassword() ? 'text' : 'password'"
                    formControlName="password"
                    placeholder="Create a password"
                    [class.error]="isFieldInvalid('password')"
                  />
                  <button
                    type="button"
                    class="password-toggle"
                    (click)="togglePassword()"
                  >
                    {{ showPassword() ? '👁️' : '👁️‍🗨️' }}
                  </button>
                </div>
                @if (isFieldInvalid('password')) {
                  <div class="error-message">
                    @if (registerForm.get('password')?.errors?.['required']) {
                      Password is required
                    } @else if (registerForm.get('password')?.errors?.['minlength']) {
                      Password must be at least 8 characters long
                    } @else if (registerForm.get('password')?.errors?.['pattern']) {
                      Password must contain at least one uppercase letter, one lowercase letter, and one number
                    }
                  </div>
                }
                @if (registerForm.get('password')?.value) {
                  <div class="password-strength">
                    <div class="strength-bar">
                      <div 
                        class="strength-fill" 
                        [class]="getPasswordStrength()"
                      ></div>
                    </div>
                    <span class="strength-text">{{ getPasswordStrengthText() }}</span>
                  </div>
                }
              </div>

              <div class="form-group">
                <label for="confirmPassword">Confirm Password *</label>
                <div class="password-input">
                  <input
                    id="confirmPassword"
                    [type]="showConfirmPassword() ? 'text' : 'password'"
                    formControlName="confirmPassword"
                    placeholder="Confirm your password"
                    [class.error]="isFieldInvalid('confirmPassword')"
                  />
                  <button
                    type="button"
                    class="password-toggle"
                    (click)="toggleConfirmPassword()"
                  >
                    {{ showConfirmPassword() ? '👁️' : '👁️‍🗨️' }}
                  </button>
                </div>
                @if (isFieldInvalid('confirmPassword')) {
                  <div class="error-message">
                    @if (registerForm.get('confirmPassword')?.errors?.['required']) {
                      Please confirm your password
                    } @else if (registerForm.errors?.['passwordMismatch']) {
                      Passwords do not match
                    }
                  </div>
                }
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" formControlName="agreeToTerms" />
                  <span class="checkmark"></span>
                  I agree to the <a href="/terms" target="_blank">Terms of Service</a> and <a href="/privacy" target="_blank">Privacy Policy</a>
                </label>
                @if (isFieldInvalid('agreeToTerms')) {
                  <div class="error-message">You must agree to the terms and conditions</div>
                }
              </div>

              <div class="form-group">
                <label class="checkbox-label">
                  <input type="checkbox" formControlName="subscribeNewsletter" />
                  <span class="checkmark"></span>
                  Subscribe to our newsletter for travel tips and deals
                </label>
              </div>
            </div>
          }

          @if (errorMessage()) {
            <div class="alert alert-error">
              {{ errorMessage() }}
            </div>
          }

          <!-- Navigation Buttons -->
          <div class="form-navigation">
            @if (currentStep() > 1) {
              <button
                type="button"
                class="btn btn-secondary"
                (click)="previousStep()"
              >
                Previous
              </button>
            }

            @if (currentStep() < 3) {
              <button
                type="button"
                class="btn btn-primary"
                (click)="nextStep()"
                [disabled]="!isCurrentStepValid()"
              >
                Next
              </button>
            } @else {
              <button
                type="submit"
                class="btn btn-primary"
                [disabled]="registerForm.invalid || isLoading()"
              >
                @if (isLoading()) {
                  <span class="spinner"></span>
                  Creating Account...
                } @else {
                  Create Account
                }
              </button>
            }
          </div>
        </form>

        <div class="auth-footer">
          <p>
            Already have an account?
            <a routerLink="/auth/login">Sign in here</a>
          </p>
        </div>
      </div>
    </div>
  `,
  styleUrls: ['../login/login.component.scss', './register.component.scss']
})
export class RegisterComponent {
  private readonly fb = inject(FormBuilder);
  private readonly authService = inject(AuthService);
  private readonly notificationService = inject(NotificationService);
  private readonly router = inject(Router);

  registerForm: FormGroup;
  isLoading = signal<boolean>(false);
  showPassword = signal<boolean>(false);
  showConfirmPassword = signal<boolean>(false);
  errorMessage = signal<string>('');
  currentStep = signal<number>(1);

  constructor() {
    this.registerForm = this.fb.group({
      firstName: ['', [Validators.required, Validators.minLength(2)]],
      lastName: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phoneNumber: [''],
      dateOfBirth: [''],
      gender: [''],
      addressLine1: ['', [Validators.required, Validators.maxLength(200)]],
      addressLine2: ['', [Validators.required, Validators.maxLength(200)]],
      city: ['', [Validators.required, Validators.maxLength(100)]],
      state: ['', [Validators.required, Validators.maxLength(100)]],
      country: ['', [Validators.required, Validators.maxLength(100)]],
      postalCode: ['', [Validators.required, Validators.maxLength(20)]],
      password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
      ]],
      confirmPassword: ['', [Validators.required]],
      agreeToTerms: [false, [Validators.requiredTrue]],
      subscribeNewsletter: [false]
    }, {
      validators: this.passwordMatchValidator
    });
  }

  passwordMatchValidator(control: AbstractControl): { [key: string]: boolean } | null {
    const password = control.get('password');
    const confirmPassword = control.get('confirmPassword');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  isFieldInvalid(fieldName: string): boolean {
    const field = this.registerForm.get(fieldName);
    return !!(field && field.invalid && (field.dirty || field.touched));
  }

  togglePassword(): void {
    this.showPassword.set(!this.showPassword());
  }

  toggleConfirmPassword(): void {
    this.showConfirmPassword.set(!this.showConfirmPassword());
  }

  nextStep(): void {
    if (this.isCurrentStepValid()) {
      this.currentStep.set(this.currentStep() + 1);
    }
  }

  previousStep(): void {
    if (this.currentStep() > 1) {
      this.currentStep.set(this.currentStep() - 1);
    }
  }

  isCurrentStepValid(): boolean {
    const step = this.currentStep();

    if (step === 1) {
      const step1Fields = ['firstName', 'lastName', 'email'];
      return step1Fields.every(field => {
        const control = this.registerForm.get(field);
        return control && control.valid;
      });
    }

    if (step === 2) {
      const step2Fields = ['addressLine1', 'addressLine2', 'city', 'state', 'country', 'postalCode'];
      return step2Fields.every(field => {
        const control = this.registerForm.get(field);
        return control && control.valid;
      });
    }

    if (step === 3) {
      const step3Fields = ['password', 'confirmPassword', 'agreeToTerms'];
      return step3Fields.every(field => {
        const control = this.registerForm.get(field);
        return control && control.valid;
      }) && !this.registerForm.errors?.['passwordMismatch'];
    }

    return false;
  }

  getPasswordStrength(): string {
    const password = this.registerForm.get('password')?.value || '';
    let strength = 0;

    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/\d/.test(password)) strength++;
    if (/[^a-zA-Z\d]/.test(password)) strength++;

    if (strength <= 2) return 'weak';
    if (strength <= 3) return 'medium';
    return 'strong';
  }

  getPasswordStrengthText(): string {
    const strength = this.getPasswordStrength();
    switch (strength) {
      case 'weak': return 'Weak';
      case 'medium': return 'Medium';
      case 'strong': return 'Strong';
      default: return '';
    }
  }

  async onSubmit(): Promise<void> {
    if (this.registerForm.valid) {
      this.isLoading.set(true);
      this.errorMessage.set('');

      try {
        const formValue = this.registerForm.value;
        const registerData = {
          firstName: formValue.firstName,
          lastName: formValue.lastName,
          email: formValue.email,
          phoneNumber: formValue.phoneNumber || undefined,
          dateOfBirth: formValue.dateOfBirth || undefined,
          gender: formValue.gender ? parseInt(formValue.gender) : undefined,
          addressLine1: formValue.addressLine1,
          addressLine2: formValue.addressLine2,
          city: formValue.city,
          state: formValue.state,
          country: formValue.country,
          postalCode: formValue.postalCode,
          password: formValue.password,
          confirmPassword: formValue.confirmPassword
        };

        const result = await firstValueFrom(this.authService.register(registerData));

        if (result?.success) {
          this.notificationService.showSuccess(
            'Account created successfully! Please check your email to verify your account.'
          );
          this.router.navigate(['/auth/verify-email'], {
            queryParams: { email: registerData.email }
          });
        } else {
          this.errorMessage.set(result?.message || 'Registration failed');
        }
      } catch (error: any) {
        this.errorMessage.set(
          error?.error?.message || 'An error occurred during registration'
        );
      } finally {
        this.isLoading.set(false);
      }
    } else {
      // Mark all fields as touched to show validation errors
      Object.keys(this.registerForm.controls).forEach(key => {
        this.registerForm.get(key)?.markAsTouched();
      });
    }
  }
}
