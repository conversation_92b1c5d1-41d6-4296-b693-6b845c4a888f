import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NotificationService, Notification } from '../../../core/services/notification.service';

@Component({
  selector: 'app-notification',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="notification-container">
      @for (notification of notifications(); track notification.id) {
        <div
          class="notification"
          [class]="'notification-' + notification.type"
        >
          <div class="notification-content">
            <div class="notification-icon">
              @switch (notification.type) {
                @case ('success') { ✅ }
                @case ('error') { ❌ }
                @case ('warning') { ⚠️ }
                @case ('info') { ℹ️ }
              }
            </div>
            
            <div class="notification-text">
              @if (notification.title) {
                <div class="notification-title">{{ notification.title }}</div>
              }
              <div class="notification-message">{{ notification.message }}</div>
            </div>

            @if (notification.actions && notification.actions.length > 0) {
              <div class="notification-actions">
                @for (action of notification.actions; track action.label) {
                  <button 
                    class="notification-action"
                    [class]="'action-' + (action.style || 'primary')"
                    (click)="executeAction(action, notification.id)"
                  >
                    {{ action.label }}
                  </button>
                }
              </div>
            }

            @if (!notification.persistent) {
              <button 
                class="notification-close"
                (click)="removeNotification(notification.id)"
                title="Close"
              >
                ✕
              </button>
            }
          </div>

          @if (!notification.persistent && notification.duration && notification.duration > 0) {
            <div 
              class="notification-progress"
              [style.animation-duration.ms]="notification.duration"
            ></div>
          }
        </div>
      }
    </div>
  `,
  styleUrls: ['./notification.component.scss'],
  animations: []
})
export class NotificationComponent {
  private readonly notificationService = inject(NotificationService);

  notifications = this.notificationService.notifications;

  removeNotification(id: string): void {
    this.notificationService.removeNotification(id);
  }

  executeAction(action: any, notificationId: string): void {
    action.action();
    this.removeNotification(notificationId);
  }
}
