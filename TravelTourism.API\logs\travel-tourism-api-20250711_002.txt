[2025-07-11 00:09:23.745 +03:00 WRN] Entity 'Blog' has a global query filter defined and is the required end of a relationship with the entity 'BlogImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:09:23.823 +03:00 WRN] Entity 'Booking' has a global query filter defined and is the required end of a relationship with the entity 'BookingPayment'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:09:23.831 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:09:23.841 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripItinerary'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:09:23.848 +03:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:09:24.086 +03:00 WRN] The 'UserRole' property 'Role' on entity type 'User' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'UserRole' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-07-11 00:09:24.119 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Currency'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
