import { Injectable, signal, inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { SwUpdate, VersionReadyEvent } from '@angular/service-worker';
import { filter, map } from 'rxjs/operators';
import { NotificationService } from './notification.service';

export interface PWAInstallPrompt {
  prompt(): Promise<void>;
  userChoice: Promise<{ outcome: 'accepted' | 'dismissed' }>;
}

export interface NetworkStatus {
  isOnline: boolean;
  connectionType: string;
  effectiveType: string;
  downlink: number;
  rtt: number;
}

@Injectable({
  providedIn: 'root'
})
export class PWAService {
  private readonly swUpdate = inject(SwUpdate);
  private readonly notificationService = inject(NotificationService);
  private readonly platformId = inject(PLATFORM_ID);
  private readonly isBrowser = isPlatformBrowser(this.platformId);

  // Signals for reactive state
  private readonly isOnline = signal<boolean>(navigator.onLine);
  private readonly isInstallable = signal<boolean>(false);
  private readonly isInstalled = signal<boolean>(false);
  private readonly updateAvailable = signal<boolean>(false);
  private readonly networkStatus = signal<NetworkStatus | null>(null);

  private deferredPrompt: PWAInstallPrompt | null = null;

  constructor() {
    if (this.isBrowser) {
      this.initializeServiceWorker();
      this.initializeNetworkMonitoring();
      this.initializeInstallPrompt();
      this.checkIfInstalled();
    }
  }

  private initializeServiceWorker(): void {
    if (!this.swUpdate.isEnabled) {
      console.log('Service Worker not enabled');
      return;
    }

    // Check for updates
    this.swUpdate.versionUpdates
      .pipe(
        filter((evt): evt is VersionReadyEvent => evt.type === 'VERSION_READY'),
        map(evt => ({
          type: 'UPDATE_AVAILABLE',
          current: evt.currentVersion,
          available: evt.latestVersion,
        }))
      )
      .subscribe(() => {
        this.updateAvailable.set(true);
        this.showUpdateNotification();
      });

    // Check for updates periodically
    this.checkForUpdates();
    setInterval(() => this.checkForUpdates(), 60000); // Check every minute
  }

  private initializeNetworkMonitoring(): void {
    if (!this.isBrowser) {
      return;
    }

    // Basic online/offline detection
    window.addEventListener('online', () => {
      this.isOnline.set(true);
      this.updateNetworkStatus();
      this.notificationService.showSuccess('Connection restored!');
    });

    window.addEventListener('offline', () => {
      this.isOnline.set(false);
      this.updateNetworkStatus();
      this.notificationService.showWarning('You are now offline. Some features may be limited.');
    });

    // Advanced network information (if available)
    this.updateNetworkStatus();
  }

  private updateNetworkStatus(): void {
    const connection = (navigator as any).connection || 
                      (navigator as any).mozConnection || 
                      (navigator as any).webkitConnection;

    if (connection) {
      this.networkStatus.set({
        isOnline: navigator.onLine,
        connectionType: connection.type || 'unknown',
        effectiveType: connection.effectiveType || 'unknown',
        downlink: connection.downlink || 0,
        rtt: connection.rtt || 0
      });
    } else {
      this.networkStatus.set({
        isOnline: navigator.onLine,
        connectionType: 'unknown',
        effectiveType: 'unknown',
        downlink: 0,
        rtt: 0
      });
    }
  }

  private initializeInstallPrompt(): void {
    if (!this.isBrowser) {
      return;
    }

    window.addEventListener('beforeinstallprompt', (e) => {
      e.preventDefault();
      this.deferredPrompt = e as any;
      this.isInstallable.set(true);
    });

    window.addEventListener('appinstalled', () => {
      this.isInstalled.set(true);
      this.isInstallable.set(false);
      this.deferredPrompt = null;
      this.notificationService.showSuccess('App installed successfully!');
    });
  }

  private checkIfInstalled(): void {
    if (!this.isBrowser) {
      return;
    }

    // Check if app is running in standalone mode
    const isStandalone = window.matchMedia('(display-mode: standalone)').matches ||
                        (window.navigator as any).standalone ||
                        document.referrer.includes('android-app://');

    this.isInstalled.set(isStandalone);
  }

  private async checkForUpdates(): Promise<void> {
    if (this.swUpdate.isEnabled) {
      try {
        const updateFound = await this.swUpdate.checkForUpdate();
        if (updateFound) {
          console.log('Update available');
        }
      } catch (err) {
        console.error('Error checking for updates:', err);
      }
    }
  }

  private showUpdateNotification(): void {
    this.notificationService.showInfo(
      'A new version is available!',
      'Update',
      {
        persistent: true,
        actions: [
          {
            label: 'Update Now',
            action: () => this.applyUpdate(),
            style: 'primary'
          },
          {
            label: 'Later',
            action: () => {},
            style: 'secondary'
          }
        ]
      }
    );
  }

  // Public methods
  async installApp(): Promise<boolean> {
    if (!this.deferredPrompt) {
      return false;
    }

    try {
      await this.deferredPrompt.prompt();
      const choiceResult = await this.deferredPrompt.userChoice;
      
      if (choiceResult.outcome === 'accepted') {
        this.isInstallable.set(false);
        this.deferredPrompt = null;
        return true;
      }
      
      return false;
    } catch (error) {
      console.error('Error installing app:', error);
      return false;
    }
  }

  async applyUpdate(): Promise<void> {
    if (!this.swUpdate.isEnabled) {
      return;
    }

    try {
      await this.swUpdate.activateUpdate();
      if (this.isBrowser) {
        window.location.reload();
      }
    } catch (error) {
      console.error('Error applying update:', error);
      this.notificationService.showError('Failed to apply update. Please refresh manually.');
    }
  }

  async clearCache(): Promise<void> {
    if (this.isBrowser && 'caches' in window) {
      try {
        const cacheNames = await caches.keys();
        await Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        );
        this.notificationService.showSuccess('Cache cleared successfully!');
      } catch (error) {
        console.error('Error clearing cache:', error);
        this.notificationService.showError('Failed to clear cache.');
      }
    }
  }

  // Cache management for offline support
  async cacheResource(url: string, cacheName: string = 'dynamic-cache'): Promise<void> {
    if (this.isBrowser && 'caches' in window) {
      try {
        const cache = await caches.open(cacheName);
        await cache.add(url);
      } catch (error) {
        console.error('Error caching resource:', error);
      }
    }
  }

  async getCachedResource(url: string): Promise<Response | undefined> {
    if (this.isBrowser && 'caches' in window) {
      try {
        const response = await caches.match(url);
        return response;
      } catch (error) {
        console.error('Error getting cached resource:', error);
        return undefined;
      }
    }
    return undefined;
  }

  // Offline data management
  saveOfflineData(key: string, data: any): void {
    try {
      localStorage.setItem(`offline_${key}`, JSON.stringify({
        data,
        timestamp: Date.now()
      }));
    } catch (error) {
      console.error('Error saving offline data:', error);
    }
  }

  getOfflineData<T>(key: string, maxAge: number = 24 * 60 * 60 * 1000): T | null {
    try {
      const stored = localStorage.getItem(`offline_${key}`);
      if (!stored) return null;

      const { data, timestamp } = JSON.parse(stored);
      
      if (Date.now() - timestamp > maxAge) {
        localStorage.removeItem(`offline_${key}`);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error getting offline data:', error);
      return null;
    }
  }

  clearOfflineData(key?: string): void {
    try {
      if (key) {
        localStorage.removeItem(`offline_${key}`);
      } else {
        // Clear all offline data
        const keys = Object.keys(localStorage).filter(k => k.startsWith('offline_'));
        keys.forEach(k => localStorage.removeItem(k));
      }
    } catch (error) {
      console.error('Error clearing offline data:', error);
    }
  }

  // Background sync (if supported)
  async registerBackgroundSync(tag: string): Promise<void> {
    if (this.isBrowser && 'serviceWorker' in navigator && 'sync' in window.ServiceWorkerRegistration.prototype) {
      try {
        const registration = await navigator.serviceWorker.ready;
        await (registration as any).sync.register(tag);
      } catch (error) {
        console.error('Error registering background sync:', error);
      }
    }
  }

  // Push notifications (if supported)
  async requestNotificationPermission(): Promise<NotificationPermission> {
    if (this.isBrowser && 'Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }

  async subscribeToPushNotifications(): Promise<PushSubscription | null> {
    if (this.isBrowser && 'serviceWorker' in navigator && 'PushManager' in window) {
      try {
        const registration = await navigator.serviceWorker.ready;
        const subscription = await registration.pushManager.subscribe({
          userVisibleOnly: true,
          applicationServerKey: this.urlBase64ToUint8Array(
            'YOUR_VAPID_PUBLIC_KEY' // Replace with your VAPID public key
          )
        });
        return subscription;
      } catch (error) {
        console.error('Error subscribing to push notifications:', error);
        return null;
      }
    }
    return null;
  }

  private urlBase64ToUint8Array(base64String: string): Uint8Array {
    if (!this.isBrowser) {
      return new Uint8Array(0);
    }

    const padding = '='.repeat((4 - base64String.length % 4) % 4);
    const base64 = (base64String + padding)
      .replace(/-/g, '+')
      .replace(/_/g, '/');

    const rawData = window.atob(base64);
    const outputArray = new Uint8Array(rawData.length);

    for (let i = 0; i < rawData.length; ++i) {
      outputArray[i] = rawData.charCodeAt(i);
    }
    return outputArray;
  }

  // Getters for reactive state
  getOnlineStatus() {
    return this.isOnline.asReadonly();
  }

  getInstallableStatus() {
    return this.isInstallable.asReadonly();
  }

  getInstalledStatus() {
    return this.isInstalled.asReadonly();
  }

  getUpdateAvailableStatus() {
    return this.updateAvailable.asReadonly();
  }

  getNetworkStatus() {
    return this.networkStatus.asReadonly();
  }
}
