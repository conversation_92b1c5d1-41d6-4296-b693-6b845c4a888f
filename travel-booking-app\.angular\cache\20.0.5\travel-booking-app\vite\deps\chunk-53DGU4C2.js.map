{"version": 3, "sources": ["../../../../../../node_modules/@angular/animations/fesm2022/private_export.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.6\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * @description Constants for the categories of parameters that can be defined for animations.\n *\n * A corresponding function defines a set of parameters for each category, and\n * collects them into a corresponding `AnimationMetadata` object.\n *\n * @publicApi\n */\nvar AnimationMetadataType;\n(function (AnimationMetadataType) {\n    /**\n     * Associates a named animation state with a set of CSS styles.\n     * See [`state()`](api/animations/state)\n     */\n    AnimationMetadataType[AnimationMetadataType[\"State\"] = 0] = \"State\";\n    /**\n     * Data for a transition from one animation state to another.\n     * See `transition()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Transition\"] = 1] = \"Transition\";\n    /**\n     * Contains a set of animation steps.\n     * See `sequence()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Sequence\"] = 2] = \"Sequence\";\n    /**\n     * Contains a set of animation steps.\n     * See `group()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Group\"] = 3] = \"Group\";\n    /**\n     * Contains an animation step.\n     * See `animate()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Animate\"] = 4] = \"Animate\";\n    /**\n     * Contains a set of animation steps.\n     * See `keyframes()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Keyframes\"] = 5] = \"Keyframes\";\n    /**\n     * Contains a set of CSS property-value pairs into a named style.\n     * See `style()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Style\"] = 6] = \"Style\";\n    /**\n     * Associates an animation with an entry trigger that can be attached to an element.\n     * See `trigger()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Trigger\"] = 7] = \"Trigger\";\n    /**\n     * Contains a re-usable animation.\n     * See `animation()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Reference\"] = 8] = \"Reference\";\n    /**\n     * Contains data to use in executing child animations returned by a query.\n     * See `animateChild()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"AnimateChild\"] = 9] = \"AnimateChild\";\n    /**\n     * Contains animation parameters for a re-usable animation.\n     * See `useAnimation()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"AnimateRef\"] = 10] = \"AnimateRef\";\n    /**\n     * Contains child-animation query data.\n     * See `query()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Query\"] = 11] = \"Query\";\n    /**\n     * Contains data for staggering an animation sequence.\n     * See `stagger()`\n     */\n    AnimationMetadataType[AnimationMetadataType[\"Stagger\"] = 12] = \"Stagger\";\n})(AnimationMetadataType || (AnimationMetadataType = {}));\n/**\n * Specifies automatic styling.\n *\n * @publicApi\n */\nconst AUTO_STYLE = '*';\n/**\n * Creates a named animation trigger, containing a  list of [`state()`](api/animations/state)\n * and `transition()` entries to be evaluated when the expression\n * bound to the trigger changes.\n *\n * @param name An identifying string.\n * @param definitions  An animation definition object, containing an array of\n * [`state()`](api/animations/state) and `transition()` declarations.\n *\n * @return An object that encapsulates the trigger data.\n *\n * @usageNotes\n * Define an animation trigger in the `animations` section of `@Component` metadata.\n * In the template, reference the trigger by name and bind it to a trigger expression that\n * evaluates to a defined animation state, using the following format:\n *\n * `[@triggerName]=\"expression\"`\n *\n * Animation trigger bindings convert all values to strings, and then match the\n * previous and current values against any linked transitions.\n * Booleans can be specified as `1` or `true` and `0` or `false`.\n *\n * ### Usage Example\n *\n * The following example creates an animation trigger reference based on the provided\n * name value.\n * The provided animation value is expected to be an array consisting of state and\n * transition declarations.\n *\n * ```ts\n * @Component({\n *   selector: \"my-component\",\n *   templateUrl: \"my-component-tpl.html\",\n *   animations: [\n *     trigger(\"myAnimationTrigger\", [\n *       state(...),\n *       state(...),\n *       transition(...),\n *       transition(...)\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"something\";\n * }\n * ```\n *\n * The template associated with this component makes use of the defined trigger\n * by binding to an element within its template code.\n *\n * ```html\n * <!-- somewhere inside of my-component-tpl.html -->\n * <div [@myAnimationTrigger]=\"myStatusExp\">...</div>\n * ```\n *\n * ### Using an inline function\n * The `transition` animation method also supports reading an inline function which can decide\n * if its associated animation should be run.\n *\n * ```ts\n * // this method is run each time the `myAnimationTrigger` trigger value changes.\n * function myInlineMatcherFn(fromState: string, toState: string, element: any, params: {[key:\n string]: any}): boolean {\n *   // notice that `element` and `params` are also available here\n *   return toState == 'yes-please-animate';\n * }\n *\n * @Component({\n *   selector: 'my-component',\n *   templateUrl: 'my-component-tpl.html',\n *   animations: [\n *     trigger('myAnimationTrigger', [\n *       transition(myInlineMatcherFn, [\n *         // the animation sequence code\n *       ]),\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   myStatusExp = \"yes-please-animate\";\n * }\n * ```\n *\n * ### Disabling Animations\n * When true, the special animation control binding `@.disabled` binding prevents\n * all animations from rendering.\n * Place the  `@.disabled` binding on an element to disable\n * animations on the element itself, as well as any inner animation triggers\n * within the element.\n *\n * The following example shows how to use this feature:\n *\n * ```angular-ts\n * @Component({\n *   selector: 'my-component',\n *   template: `\n *     <div [@.disabled]=\"isDisabled\">\n *       <div [@childAnimation]=\"exp\"></div>\n *     </div>\n *   `,\n *   animations: [\n *     trigger(\"childAnimation\", [\n *       // ...\n *     ])\n *   ]\n * })\n * class MyComponent {\n *   isDisabled = true;\n *   exp = '...';\n * }\n * ```\n *\n * When `@.disabled` is true, it prevents the `@childAnimation` trigger from animating,\n * along with any inner animations.\n *\n * ### Disable animations application-wide\n * When an area of the template is set to have animations disabled,\n * **all** inner components have their animations disabled as well.\n * This means that you can disable all animations for an app\n * by placing a host binding set on `@.disabled` on the topmost Angular component.\n *\n * ```ts\n * import {Component, HostBinding} from '@angular/core';\n *\n * @Component({\n *   selector: 'app-component',\n *   templateUrl: 'app.component.html',\n * })\n * class AppComponent {\n *   @HostBinding('@.disabled')\n *   public animationsDisabled = true;\n * }\n * ```\n *\n * ### Overriding disablement of inner animations\n * Despite inner animations being disabled, a parent animation can `query()`\n * for inner elements located in disabled areas of the template and still animate\n * them if needed. This is also the case for when a sub animation is\n * queried by a parent and then later animated using `animateChild()`.\n *\n * ### Detecting when an animation is disabled\n * If a region of the DOM (or the entire application) has its animations disabled, the animation\n * trigger callbacks still fire, but for zero seconds. When the callback fires, it provides\n * an instance of an `AnimationEvent`. If animations are disabled,\n * the `.disabled` flag on the event is true.\n *\n * @publicApi\n */\nfunction trigger(name, definitions) {\n    return { type: AnimationMetadataType.Trigger, name, definitions, options: {} };\n}\n/**\n * Defines an animation step that combines styling information with timing information.\n *\n * @param timings Sets `AnimateTimings` for the parent animation.\n * A string in the format \"duration [delay] [easing]\".\n *  - Duration and delay are expressed as a number and optional time unit,\n * such as \"1s\" or \"10ms\" for one second and 10 milliseconds, respectively.\n * The default unit is milliseconds.\n *  - The easing value controls how the animation accelerates and decelerates\n * during its runtime. Value is one of  `ease`, `ease-in`, `ease-out`,\n * `ease-in-out`, or a `cubic-bezier()` function call.\n * If not supplied, no easing is applied.\n *\n * For example, the string \"1s 100ms ease-out\" specifies a duration of\n * 1000 milliseconds, and delay of 100 ms, and the \"ease-out\" easing style,\n * which decelerates near the end of the duration.\n * @param styles Sets AnimationStyles for the parent animation.\n * A function call to either `style()` or `keyframes()`\n * that returns a collection of CSS style entries to be applied to the parent animation.\n * When null, uses the styles from the destination state.\n * This is useful when describing an animation step that will complete an animation;\n * see \"Animating to the final state\" in `transitions()`.\n * @returns An object that encapsulates the animation step.\n *\n * @usageNotes\n * Call within an animation `sequence()`, {@link /api/animations/group group()}, or\n * `transition()` call to specify an animation step\n * that applies given style data to the parent animation for a given amount of time.\n *\n * ### Syntax Examples\n * **Timing examples**\n *\n * The following examples show various `timings` specifications.\n * - `animate(500)` : Duration is 500 milliseconds.\n * - `animate(\"1s\")` : Duration is 1000 milliseconds.\n * - `animate(\"100ms 0.5s\")` : Duration is 100 milliseconds, delay is 500 milliseconds.\n * - `animate(\"5s ease-in\")` : Duration is 5000 milliseconds, easing in.\n * - `animate(\"5s 10ms cubic-bezier(.17,.67,.88,.1)\")` : Duration is 5000 milliseconds, delay is 10\n * milliseconds, easing according to a bezier curve.\n *\n * **Style examples**\n *\n * The following example calls `style()` to set a single CSS style.\n * ```ts\n * animate(500, style({ background: \"red\" }))\n * ```\n * The following example calls `keyframes()` to set a CSS style\n * to different values for successive keyframes.\n * ```ts\n * animate(500, keyframes(\n *  [\n *   style({ background: \"blue\" }),\n *   style({ background: \"red\" })\n *  ])\n * ```\n *\n * @publicApi\n */\nfunction animate(timings, styles = null) {\n    return { type: AnimationMetadataType.Animate, styles, timings };\n}\n/**\n * @description Defines a list of animation steps to be run in parallel.\n *\n * @param steps An array of animation step objects.\n * - When steps are defined by `style()` or `animate()`\n * function calls, each call within the group is executed instantly.\n * - To specify offset styles to be applied at a later time, define steps with\n * `keyframes()`, or use `animate()` calls with a delay value.\n * For example:\n *\n * ```ts\n * group([\n *   animate(\"1s\", style({ background: \"black\" })),\n *   animate(\"2s\", style({ color: \"white\" }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the group data.\n *\n * @usageNotes\n * Grouped animations are useful when a series of styles must be\n * animated at different starting times and closed off at different ending times.\n *\n * When called within a `sequence()` or a\n * `transition()` call, does not continue to the next\n * instruction until all of the inner animation steps have completed.\n *\n * @publicApi\n */\nfunction group(steps, options = null) {\n    return { type: AnimationMetadataType.Group, steps, options };\n}\n/**\n * Defines a list of animation steps to be run sequentially, one by one.\n *\n * @param steps An array of animation step objects.\n * - Steps defined by `style()` calls apply the styling data immediately.\n * - Steps defined by `animate()` calls apply the styling data over time\n *   as specified by the timing data.\n *\n * ```ts\n * sequence([\n *   style({ opacity: 0 }),\n *   animate(\"1s\", style({ opacity: 1 }))\n * ])\n * ```\n *\n * @param options An options object containing a delay and\n * developer-defined parameters that provide styling defaults and\n * can be overridden on invocation.\n *\n * @return An object that encapsulates the sequence data.\n *\n * @usageNotes\n * When you pass an array of steps to a\n * `transition()` call, the steps run sequentially by default.\n * Compare this to the  {@link /api/animations/group group()} call, which runs animation steps in\n *parallel.\n *\n * When a sequence is used within a  {@link /api/animations/group group()} or a `transition()` call,\n * execution continues to the next instruction only after each of the inner animation\n * steps have completed.\n *\n * @publicApi\n **/\nfunction sequence(steps, options = null) {\n    return { type: AnimationMetadataType.Sequence, steps, options };\n}\n/**\n * Declares a key/value object containing CSS properties/styles that\n * can then be used for an animation [`state`](api/animations/state), within an animation\n *`sequence`, or as styling data for calls to `animate()` and `keyframes()`.\n *\n * @param tokens A set of CSS styles or HTML styles associated with an animation state.\n * The value can be any of the following:\n * - A key-value style pair associating a CSS property with a value.\n * - An array of key-value style pairs.\n * - An asterisk (*), to use auto-styling, where styles are derived from the element\n * being animated and applied to the animation when it starts.\n *\n * Auto-styling can be used to define a state that depends on layout or other\n * environmental factors.\n *\n * @return An object that encapsulates the style data.\n *\n * @usageNotes\n * The following examples create animation styles that collect a set of\n * CSS property values:\n *\n * ```ts\n * // string values for CSS properties\n * style({ background: \"red\", color: \"blue\" })\n *\n * // numerical pixel values\n * style({ width: 100, height: 0 })\n * ```\n *\n * The following example uses auto-styling to allow an element to animate from\n * a height of 0 up to its full height:\n *\n * ```ts\n * style({ height: 0 }),\n * animate(\"1s\", style({ height: \"*\" }))\n * ```\n *\n * @publicApi\n **/\nfunction style(tokens) {\n    return { type: AnimationMetadataType.Style, styles: tokens, offset: null };\n}\n/**\n * Declares an animation state within a trigger attached to an element.\n *\n * @param name One or more names for the defined state in a comma-separated string.\n * The following reserved state names can be supplied to define a style for specific use\n * cases:\n *\n * - `void` You can associate styles with this name to be used when\n * the element is detached from the application. For example, when an `ngIf` evaluates\n * to false, the state of the associated element is void.\n *  - `*` (asterisk) Indicates the default state. You can associate styles with this name\n * to be used as the fallback when the state that is being animated is not declared\n * within the trigger.\n *\n * @param styles A set of CSS styles associated with this state, created using the\n * `style()` function.\n * This set of styles persists on the element once the state has been reached.\n * @param options Parameters that can be passed to the state when it is invoked.\n * 0 or more key-value pairs.\n * @return An object that encapsulates the new state data.\n *\n * @usageNotes\n * Use the `trigger()` function to register states to an animation trigger.\n * Use the `transition()` function to animate between states.\n * When a state is active within a component, its associated styles persist on the element,\n * even when the animation ends.\n *\n * @publicApi\n **/\nfunction state(name, styles, options) {\n    return { type: AnimationMetadataType.State, name, styles, options };\n}\n/**\n * Defines a set of animation styles, associating each style with an optional `offset` value.\n *\n * @param steps A set of animation styles with optional offset data.\n * The optional `offset` value for a style specifies a percentage of the total animation\n * time at which that style is applied.\n * @returns An object that encapsulates the keyframes data.\n *\n * @usageNotes\n * Use with the `animate()` call. Instead of applying animations\n * from the current state\n * to the destination state, keyframes describe how each style entry is applied and at what point\n * within the animation arc.\n * Compare [CSS Keyframe Animations](https://www.w3schools.com/css/css3_animations.asp).\n *\n * ### Usage\n *\n * In the following example, the offset values describe\n * when each `backgroundColor` value is applied. The color is red at the start, and changes to\n * blue when 20% of the total time has elapsed.\n *\n * ```ts\n * // the provided offset values\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\", offset: 0 }),\n *   style({ backgroundColor: \"blue\", offset: 0.2 }),\n *   style({ backgroundColor: \"orange\", offset: 0.3 }),\n *   style({ backgroundColor: \"black\", offset: 1 })\n * ]))\n * ```\n *\n * If there are no `offset` values specified in the style entries, the offsets\n * are calculated automatically.\n *\n * ```ts\n * animate(\"5s\", keyframes([\n *   style({ backgroundColor: \"red\" }) // offset = 0\n *   style({ backgroundColor: \"blue\" }) // offset = 0.33\n *   style({ backgroundColor: \"orange\" }) // offset = 0.66\n *   style({ backgroundColor: \"black\" }) // offset = 1\n * ]))\n *```\n\n * @publicApi\n */\nfunction keyframes(steps) {\n    return { type: AnimationMetadataType.Keyframes, steps };\n}\n/**\n * Declares an animation transition which is played when a certain specified condition is met.\n *\n * @param stateChangeExpr A string with a specific format or a function that specifies when the\n * animation transition should occur (see [State Change Expression](#state-change-expression)).\n *\n * @param steps One or more animation objects that represent the animation's instructions.\n *\n * @param options An options object that can be used to specify a delay for the animation or provide\n * custom parameters for it.\n *\n * @returns An object that encapsulates the transition data.\n *\n * @usageNotes\n *\n * ### State Change Expression\n *\n * The State Change Expression instructs Angular when to run the transition's animations, it can\n *either be\n *  - a string with a specific syntax\n *  - or a function that compares the previous and current state (value of the expression bound to\n *    the element's trigger) and returns `true` if the transition should occur or `false` otherwise\n *\n * The string format can be:\n *  - `fromState => toState`, which indicates that the transition's animations should occur then the\n *    expression bound to the trigger's element goes from `fromState` to `toState`\n *\n *    _Example:_\n *      ```ts\n *        transition('open => closed', animate('.5s ease-out', style({ height: 0 }) ))\n *      ```\n *\n *  - `fromState <=> toState`, which indicates that the transition's animations should occur then\n *    the expression bound to the trigger's element goes from `fromState` to `toState` or vice versa\n *\n *    _Example:_\n *      ```ts\n *        transition('enabled <=> disabled', animate('1s cubic-bezier(0.8,0.3,0,1)'))\n *      ```\n *\n *  - `:enter`/`:leave`, which indicates that the transition's animations should occur when the\n *    element enters or exists the DOM\n *\n *    _Example:_\n *      ```ts\n *        transition(':enter', [\n *          style({ opacity: 0 }),\n *          animate('500ms', style({ opacity: 1 }))\n *        ])\n *      ```\n *\n *  - `:increment`/`:decrement`, which indicates that the transition's animations should occur when\n *    the numerical expression bound to the trigger's element has increased in value or decreased\n *\n *    _Example:_\n *      ```ts\n *        transition(':increment', query('@counter', animateChild()))\n *      ```\n *\n *  - a sequence of any of the above divided by commas, which indicates that transition's animations\n *    should occur whenever one of the state change expressions matches\n *\n *    _Example:_\n *      ```ts\n *        transition(':increment, * => enabled, :enter', animate('1s ease', keyframes([\n *          style({ transform: 'scale(1)', offset: 0}),\n *          style({ transform: 'scale(1.1)', offset: 0.7}),\n *          style({ transform: 'scale(1)', offset: 1})\n *        ]))),\n *      ```\n *\n * Also note that in such context:\n *  - `void` can be used to indicate the absence of the element\n *  - asterisks can be used as wildcards that match any state\n *  - (as a consequence of the above, `void => *` is equivalent to `:enter` and `* => void` is\n *    equivalent to `:leave`)\n *  - `true` and `false` also match expression values of `1` and `0` respectively (but do not match\n *    _truthy_ and _falsy_ values)\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n *  Be careful about entering end leaving elements as their transitions present a common\n *  pitfall for developers.\n *\n *  Note that when an element with a trigger enters the DOM its `:enter` transition always\n *  gets executed, but its `:leave` transition will not be executed if the element is removed\n *  alongside its parent (as it will be removed \"without warning\" before its transition has\n *  a chance to be executed, the only way that such transition can occur is if the element\n *  is exiting the DOM on its own).\n *\n *\n * </div>\n *\n * ### Animating to a Final State\n *\n * If the final step in a transition is a call to `animate()` that uses a timing value\n * with no `style` data, that step is automatically considered the final animation arc,\n * for the element to reach the final state, in such case Angular automatically adds or removes\n * CSS styles to ensure that the element is in the correct final state.\n *\n *\n * ### Usage Examples\n *\n *  - Transition animations applied based on\n *    the trigger's expression value\n *\n *   ```html\n *   <div [@myAnimationTrigger]=\"myStatusExp\">\n *    ...\n *   </div>\n *   ```\n *\n *   ```ts\n *   trigger(\"myAnimationTrigger\", [\n *     ..., // states\n *     transition(\"on => off, open => closed\", animate(500)),\n *     transition(\"* <=> error\", query('.indicator', animateChild()))\n *   ])\n *   ```\n *\n *  - Transition animations applied based on custom logic dependent\n *    on the trigger's expression value and provided parameters\n *\n *    ```html\n *    <div [@myAnimationTrigger]=\"{\n *     value: stepName,\n *     params: { target: currentTarget }\n *    }\">\n *     ...\n *    </div>\n *    ```\n *\n *    ```ts\n *    trigger(\"myAnimationTrigger\", [\n *      ..., // states\n *      transition(\n *        (fromState, toState, _element, params) =>\n *          ['firststep', 'laststep'].includes(fromState.toLowerCase())\n *          && toState === params?.['target'],\n *        animate('1s')\n *      )\n *    ])\n *    ```\n *\n * @publicApi\n **/\nfunction transition(stateChangeExpr, steps, options = null) {\n    return { type: AnimationMetadataType.Transition, expr: stateChangeExpr, animation: steps, options };\n}\n/**\n * Produces a reusable animation that can be invoked in another animation or sequence,\n * by calling the `useAnimation()` function.\n *\n * @param steps One or more animation objects, as returned by the `animate()`\n * or `sequence()` function, that form a transformation from one state to another.\n * A sequence is used by default when you pass an array.\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional developer-defined parameters.\n * Provided values for additional parameters are used as defaults,\n * and override values can be passed to the caller on invocation.\n * @returns An object that encapsulates the animation data.\n *\n * @usageNotes\n * The following example defines a reusable animation, providing some default parameter\n * values.\n *\n * ```ts\n * var fadeAnimation = animation([\n *   style({ opacity: '{{ start }}' }),\n *   animate('{{ time }}',\n *   style({ opacity: '{{ end }}'}))\n *   ],\n *   { params: { time: '1000ms', start: 0, end: 1 }});\n * ```\n *\n * The following invokes the defined animation with a call to `useAnimation()`,\n * passing in override parameter values.\n *\n * ```js\n * useAnimation(fadeAnimation, {\n *   params: {\n *     time: '2s',\n *     start: 1,\n *     end: 0\n *   }\n * })\n * ```\n *\n * If any of the passed-in parameter values are missing from this call,\n * the default values are used. If one or more parameter values are missing before a step is\n * animated, `useAnimation()` throws an error.\n *\n * @publicApi\n */\nfunction animation(steps, options = null) {\n    return { type: AnimationMetadataType.Reference, animation: steps, options };\n}\n/**\n * Executes a queried inner animation element within an animation sequence.\n *\n * @param options An options object that can contain a delay value for the start of the\n * animation, and additional override values for developer-defined parameters.\n * @return An object that encapsulates the child animation data.\n *\n * @usageNotes\n * Each time an animation is triggered in Angular, the parent animation\n * has priority and any child animations are blocked. In order\n * for a child animation to run, the parent animation must query each of the elements\n * containing child animations, and run them using this function.\n *\n * Note that this feature is designed to be used with `query()` and it will only work\n * with animations that are assigned using the Angular animation library. CSS keyframes\n * and transitions are not handled by this API.\n *\n * @publicApi\n */\nfunction animateChild(options = null) {\n    return { type: AnimationMetadataType.AnimateChild, options };\n}\n/**\n * Starts a reusable animation that is created using the `animation()` function.\n *\n * @param animation The reusable animation to start.\n * @param options An options object that can contain a delay value for the start of\n * the animation, and additional override values for developer-defined parameters.\n * @return An object that contains the animation parameters.\n *\n * @publicApi\n */\nfunction useAnimation(animation, options = null) {\n    return { type: AnimationMetadataType.AnimateRef, animation, options };\n}\n/**\n * Finds one or more inner elements within the current element that is\n * being animated within a sequence. Use with `animate()`.\n *\n * @param selector The element to query, or a set of elements that contain Angular-specific\n * characteristics, specified with one or more of the following tokens.\n *  - `query(\":enter\")` or `query(\":leave\")` : Query for newly inserted/removed elements (not\n *     all elements can be queried via these tokens, see\n *     [Entering and Leaving Elements](#entering-and-leaving-elements))\n *  - `query(\":animating\")` : Query all currently animating elements.\n *  - `query(\"@triggerName\")` : Query elements that contain an animation trigger.\n *  - `query(\"@*\")` : Query all elements that contain an animation triggers.\n *  - `query(\":self\")` : Include the current element into the animation sequence.\n *\n * @param animation One or more animation steps to apply to the queried element or elements.\n * An array is treated as an animation sequence.\n * @param options An options object. Use the 'limit' field to limit the total number of\n * items to collect.\n * @return An object that encapsulates the query data.\n *\n * @usageNotes\n *\n * ### Multiple Tokens\n *\n * Tokens can be merged into a combined query selector string. For example:\n *\n * ```ts\n *  query(':self, .record:enter, .record:leave, @subTrigger', [...])\n * ```\n *\n * The `query()` function collects multiple elements and works internally by using\n * `element.querySelectorAll`. Use the `limit` field of an options object to limit\n * the total number of items to be collected. For example:\n *\n * ```js\n * query('div', [\n *   animate(...),\n *   animate(...)\n * ], { limit: 1 })\n * ```\n *\n * By default, throws an error when zero items are found. Set the\n * `optional` flag to ignore this error. For example:\n *\n * ```js\n * query('.some-element-that-may-not-be-there', [\n *   animate(...),\n *   animate(...)\n * ], { optional: true })\n * ```\n *\n * ### Entering and Leaving Elements\n *\n * Not all elements can be queried via the `:enter` and `:leave` tokens, the only ones\n * that can are those that Angular assumes can enter/leave based on their own logic\n * (if their insertion/removal is simply a consequence of that of their parent they\n * should be queried via a different token in their parent's `:enter`/`:leave` transitions).\n *\n * The only elements Angular assumes can enter/leave based on their own logic (thus the only\n * ones that can be queried via the `:enter` and `:leave` tokens) are:\n *  - Those inserted dynamically (via `ViewContainerRef`)\n *  - Those that have a structural directive (which, under the hood, are a subset of the above ones)\n *\n * <div class=\"docs-alert docs-alert-helpful\">\n *\n *  Note that elements will be successfully queried via `:enter`/`:leave` even if their\n *  insertion/removal is not done manually via `ViewContainerRef`or caused by their structural\n *  directive (e.g. they enter/exit alongside their parent).\n *\n * </div>\n *\n * <div class=\"docs-alert docs-alert-important\">\n *\n *  There is an exception to what previously mentioned, besides elements entering/leaving based on\n *  their own logic, elements with an animation trigger can always be queried via `:leave` when\n * their parent is also leaving.\n *\n * </div>\n *\n * ### Usage Example\n *\n * The following example queries for inner elements and animates them\n * individually using `animate()`.\n *\n * ```angular-ts\n * @Component({\n *   selector: 'inner',\n *   template: `\n *     <div [@queryAnimation]=\"exp\">\n *       <h1>Title</h1>\n *       <div class=\"content\">\n *         Blah blah blah\n *       </div>\n *     </div>\n *   `,\n *   animations: [\n *    trigger('queryAnimation', [\n *      transition('* => goAnimate', [\n *        // hide the inner elements\n *        query('h1', style({ opacity: 0 })),\n *        query('.content', style({ opacity: 0 })),\n *\n *        // animate the inner elements in, one by one\n *        query('h1', animate(1000, style({ opacity: 1 }))),\n *        query('.content', animate(1000, style({ opacity: 1 }))),\n *      ])\n *    ])\n *  ]\n * })\n * class Cmp {\n *   exp = '';\n *\n *   goAnimate() {\n *     this.exp = 'goAnimate';\n *   }\n * }\n * ```\n *\n * @publicApi\n */\nfunction query(selector, animation, options = null) {\n    return { type: AnimationMetadataType.Query, selector, animation, options };\n}\n/**\n * Use within an animation `query()` call to issue a timing gap after\n * each queried item is animated.\n *\n * @param timings A delay value.\n * @param animation One ore more animation steps.\n * @returns An object that encapsulates the stagger data.\n *\n * @usageNotes\n * In the following example, a container element wraps a list of items stamped out\n * by an `@for` block. The container element contains an animation trigger that will later be set\n * to query for each of the inner items.\n *\n * Each time items are added, the opacity fade-in animation runs,\n * and each removed item is faded out.\n * When either of these animations occur, the stagger effect is\n * applied after each item's animation is started.\n *\n * ```html\n * <!-- list.component.html -->\n * <button (click)=\"toggle()\">Show / Hide Items</button>\n * <hr />\n * <div [@listAnimation]=\"items.length\">\n *   @for(item of items; track $index) {\n *      <div>{{ item }}</div>\n *   }\n * </div>\n * ```\n *\n * Here is the component code:\n *\n * ```ts\n * import {trigger, transition, style, animate, query, stagger} from '@angular/animations';\n * @Component({\n *   templateUrl: 'list.component.html',\n *   animations: [\n *     trigger('listAnimation', [\n *     ...\n *     ])\n *   ]\n * })\n * class ListComponent {\n *   items = [];\n *\n *   showItems() {\n *     this.items = [0,1,2,3,4];\n *   }\n *\n *   hideItems() {\n *     this.items = [];\n *   }\n *\n *   toggle() {\n *     this.items.length ? this.hideItems() : this.showItems();\n *    }\n *  }\n * ```\n *\n * Here is the animation trigger code:\n *\n * ```ts\n * trigger('listAnimation', [\n *   transition('* => *', [ // each time the binding value changes\n *     query(':leave', [\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 0 }))\n *       ])\n *     ]),\n *     query(':enter', [\n *       style({ opacity: 0 }),\n *       stagger(100, [\n *         animate('0.5s', style({ opacity: 1 }))\n *       ])\n *     ])\n *   ])\n * ])\n * ```\n *\n * @publicApi\n */\nfunction stagger(timings, animation) {\n    return { type: AnimationMetadataType.Stagger, timings, animation };\n}\n\n/**\n * An empty programmatic controller for reusable animations.\n * Used internally when animations are disabled, to avoid\n * checking for the null case when an animation player is expected.\n *\n * @see {@link animate}\n * @see {@link AnimationPlayer}\n *\n * @publicApi\n */\nclass NoopAnimationPlayer {\n    _onDoneFns = [];\n    _onStartFns = [];\n    _onDestroyFns = [];\n    _originalOnDoneFns = [];\n    _originalOnStartFns = [];\n    _started = false;\n    _destroyed = false;\n    _finished = false;\n    _position = 0;\n    parentPlayer = null;\n    totalTime;\n    constructor(duration = 0, delay = 0) {\n        this.totalTime = duration + delay;\n    }\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach((fn) => fn());\n            this._onDoneFns = [];\n        }\n    }\n    onStart(fn) {\n        this._originalOnStartFns.push(fn);\n        this._onStartFns.push(fn);\n    }\n    onDone(fn) {\n        this._originalOnDoneFns.push(fn);\n        this._onDoneFns.push(fn);\n    }\n    onDestroy(fn) {\n        this._onDestroyFns.push(fn);\n    }\n    hasStarted() {\n        return this._started;\n    }\n    init() { }\n    play() {\n        if (!this.hasStarted()) {\n            this._onStart();\n            this.triggerMicrotask();\n        }\n        this._started = true;\n    }\n    /** @internal */\n    triggerMicrotask() {\n        queueMicrotask(() => this._onFinish());\n    }\n    _onStart() {\n        this._onStartFns.forEach((fn) => fn());\n        this._onStartFns = [];\n    }\n    pause() { }\n    restart() { }\n    finish() {\n        this._onFinish();\n    }\n    destroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            if (!this.hasStarted()) {\n                this._onStart();\n            }\n            this.finish();\n            this._onDestroyFns.forEach((fn) => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    reset() {\n        this._started = false;\n        this._finished = false;\n        this._onStartFns = this._originalOnStartFns;\n        this._onDoneFns = this._originalOnDoneFns;\n    }\n    setPosition(position) {\n        this._position = this.totalTime ? position * this.totalTime : 1;\n    }\n    getPosition() {\n        return this.totalTime ? this._position / this.totalTime : 1;\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach((fn) => fn());\n        methods.length = 0;\n    }\n}\n\n/**\n * A programmatic controller for a group of reusable animations.\n * Used internally to control animations.\n *\n * @see {@link AnimationPlayer}\n * @see {@link animations/group group}\n *\n */\nclass AnimationGroupPlayer {\n    _onDoneFns = [];\n    _onStartFns = [];\n    _finished = false;\n    _started = false;\n    _destroyed = false;\n    _onDestroyFns = [];\n    parentPlayer = null;\n    totalTime = 0;\n    players;\n    constructor(_players) {\n        this.players = _players;\n        let doneCount = 0;\n        let destroyCount = 0;\n        let startCount = 0;\n        const total = this.players.length;\n        if (total == 0) {\n            queueMicrotask(() => this._onFinish());\n        }\n        else {\n            this.players.forEach((player) => {\n                player.onDone(() => {\n                    if (++doneCount == total) {\n                        this._onFinish();\n                    }\n                });\n                player.onDestroy(() => {\n                    if (++destroyCount == total) {\n                        this._onDestroy();\n                    }\n                });\n                player.onStart(() => {\n                    if (++startCount == total) {\n                        this._onStart();\n                    }\n                });\n            });\n        }\n        this.totalTime = this.players.reduce((time, player) => Math.max(time, player.totalTime), 0);\n    }\n    _onFinish() {\n        if (!this._finished) {\n            this._finished = true;\n            this._onDoneFns.forEach((fn) => fn());\n            this._onDoneFns = [];\n        }\n    }\n    init() {\n        this.players.forEach((player) => player.init());\n    }\n    onStart(fn) {\n        this._onStartFns.push(fn);\n    }\n    _onStart() {\n        if (!this.hasStarted()) {\n            this._started = true;\n            this._onStartFns.forEach((fn) => fn());\n            this._onStartFns = [];\n        }\n    }\n    onDone(fn) {\n        this._onDoneFns.push(fn);\n    }\n    onDestroy(fn) {\n        this._onDestroyFns.push(fn);\n    }\n    hasStarted() {\n        return this._started;\n    }\n    play() {\n        if (!this.parentPlayer) {\n            this.init();\n        }\n        this._onStart();\n        this.players.forEach((player) => player.play());\n    }\n    pause() {\n        this.players.forEach((player) => player.pause());\n    }\n    restart() {\n        this.players.forEach((player) => player.restart());\n    }\n    finish() {\n        this._onFinish();\n        this.players.forEach((player) => player.finish());\n    }\n    destroy() {\n        this._onDestroy();\n    }\n    _onDestroy() {\n        if (!this._destroyed) {\n            this._destroyed = true;\n            this._onFinish();\n            this.players.forEach((player) => player.destroy());\n            this._onDestroyFns.forEach((fn) => fn());\n            this._onDestroyFns = [];\n        }\n    }\n    reset() {\n        this.players.forEach((player) => player.reset());\n        this._destroyed = false;\n        this._finished = false;\n        this._started = false;\n    }\n    setPosition(p) {\n        const timeAtPosition = p * this.totalTime;\n        this.players.forEach((player) => {\n            const position = player.totalTime ? Math.min(1, timeAtPosition / player.totalTime) : 1;\n            player.setPosition(position);\n        });\n    }\n    getPosition() {\n        const longestPlayer = this.players.reduce((longestSoFar, player) => {\n            const newPlayerIsLongest = longestSoFar === null || player.totalTime > longestSoFar.totalTime;\n            return newPlayerIsLongest ? player : longestSoFar;\n        }, null);\n        return longestPlayer != null ? longestPlayer.getPosition() : 0;\n    }\n    beforeDestroy() {\n        this.players.forEach((player) => {\n            if (player.beforeDestroy) {\n                player.beforeDestroy();\n            }\n        });\n    }\n    /** @internal */\n    triggerCallback(phaseName) {\n        const methods = phaseName == 'start' ? this._onStartFns : this._onDoneFns;\n        methods.forEach((fn) => fn());\n        methods.length = 0;\n    }\n}\n\nconst ɵPRE_STYLE = '!';\n\nexport { AUTO_STYLE, AnimationGroupPlayer, AnimationMetadataType, NoopAnimationPlayer, animate, animateChild, animation, group, keyframes, query, sequence, stagger, state, style, transition, trigger, useAnimation, ɵPRE_STYLE };\n\n"], "mappings": ";AAcA,IAAI;AAAA,CACH,SAAUA,wBAAuB;AAK9B,EAAAA,uBAAsBA,uBAAsB,OAAO,IAAI,CAAC,IAAI;AAK5D,EAAAA,uBAAsBA,uBAAsB,YAAY,IAAI,CAAC,IAAI;AAKjE,EAAAA,uBAAsBA,uBAAsB,UAAU,IAAI,CAAC,IAAI;AAK/D,EAAAA,uBAAsBA,uBAAsB,OAAO,IAAI,CAAC,IAAI;AAK5D,EAAAA,uBAAsBA,uBAAsB,SAAS,IAAI,CAAC,IAAI;AAK9D,EAAAA,uBAAsBA,uBAAsB,WAAW,IAAI,CAAC,IAAI;AAKhE,EAAAA,uBAAsBA,uBAAsB,OAAO,IAAI,CAAC,IAAI;AAK5D,EAAAA,uBAAsBA,uBAAsB,SAAS,IAAI,CAAC,IAAI;AAK9D,EAAAA,uBAAsBA,uBAAsB,WAAW,IAAI,CAAC,IAAI;AAKhE,EAAAA,uBAAsBA,uBAAsB,cAAc,IAAI,CAAC,IAAI;AAKnE,EAAAA,uBAAsBA,uBAAsB,YAAY,IAAI,EAAE,IAAI;AAKlE,EAAAA,uBAAsBA,uBAAsB,OAAO,IAAI,EAAE,IAAI;AAK7D,EAAAA,uBAAsBA,uBAAsB,SAAS,IAAI,EAAE,IAAI;AACnE,GAAG,0BAA0B,wBAAwB,CAAC,EAAE;AAMxD,IAAM,aAAa;AAqJnB,SAAS,QAAQ,MAAM,aAAa;AAChC,SAAO,EAAE,MAAM,sBAAsB,SAAS,MAAM,aAAa,SAAS,CAAC,EAAE;AACjF;AA2DA,SAAS,QAAQ,SAAS,SAAS,MAAM;AACrC,SAAO,EAAE,MAAM,sBAAsB,SAAS,QAAQ,QAAQ;AAClE;AAkCA,SAAS,MAAM,OAAO,UAAU,MAAM;AAClC,SAAO,EAAE,MAAM,sBAAsB,OAAO,OAAO,QAAQ;AAC/D;AAkCA,SAAS,SAAS,OAAO,UAAU,MAAM;AACrC,SAAO,EAAE,MAAM,sBAAsB,UAAU,OAAO,QAAQ;AAClE;AAwCA,SAAS,MAAM,QAAQ;AACnB,SAAO,EAAE,MAAM,sBAAsB,OAAO,QAAQ,QAAQ,QAAQ,KAAK;AAC7E;AA8BA,SAAS,MAAM,MAAM,QAAQ,SAAS;AAClC,SAAO,EAAE,MAAM,sBAAsB,OAAO,MAAM,QAAQ,QAAQ;AACtE;AA8CA,SAAS,UAAU,OAAO;AACtB,SAAO,EAAE,MAAM,sBAAsB,WAAW,MAAM;AAC1D;AAmJA,SAAS,WAAW,iBAAiB,OAAO,UAAU,MAAM;AACxD,SAAO,EAAE,MAAM,sBAAsB,YAAY,MAAM,iBAAiB,WAAW,OAAO,QAAQ;AACtG;AA8CA,SAAS,UAAU,OAAO,UAAU,MAAM;AACtC,SAAO,EAAE,MAAM,sBAAsB,WAAW,WAAW,OAAO,QAAQ;AAC9E;AAoBA,SAAS,aAAa,UAAU,MAAM;AAClC,SAAO,EAAE,MAAM,sBAAsB,cAAc,QAAQ;AAC/D;AAWA,SAAS,aAAaC,YAAW,UAAU,MAAM;AAC7C,SAAO,EAAE,MAAM,sBAAsB,YAAY,WAAAA,YAAW,QAAQ;AACxE;AAyHA,SAAS,MAAM,UAAUA,YAAW,UAAU,MAAM;AAChD,SAAO,EAAE,MAAM,sBAAsB,OAAO,UAAU,WAAAA,YAAW,QAAQ;AAC7E;AAiFA,SAAS,QAAQ,SAASA,YAAW;AACjC,SAAO,EAAE,MAAM,sBAAsB,SAAS,SAAS,WAAAA,WAAU;AACrE;AAYA,IAAM,sBAAN,MAA0B;AAAA,EACtB,aAAa,CAAC;AAAA,EACd,cAAc,CAAC;AAAA,EACf,gBAAgB,CAAC;AAAA,EACjB,qBAAqB,CAAC;AAAA,EACtB,sBAAsB,CAAC;AAAA,EACvB,WAAW;AAAA,EACX,aAAa;AAAA,EACb,YAAY;AAAA,EACZ,YAAY;AAAA,EACZ,eAAe;AAAA,EACf;AAAA,EACA,YAAY,WAAW,GAAG,QAAQ,GAAG;AACjC,SAAK,YAAY,WAAW;AAAA,EAChC;AAAA,EACA,YAAY;AACR,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,YAAY;AACjB,WAAK,WAAW,QAAQ,CAAC,OAAO,GAAG,CAAC;AACpC,WAAK,aAAa,CAAC;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,QAAQ,IAAI;AACR,SAAK,oBAAoB,KAAK,EAAE;AAChC,SAAK,YAAY,KAAK,EAAE;AAAA,EAC5B;AAAA,EACA,OAAO,IAAI;AACP,SAAK,mBAAmB,KAAK,EAAE;AAC/B,SAAK,WAAW,KAAK,EAAE;AAAA,EAC3B;AAAA,EACA,UAAU,IAAI;AACV,SAAK,cAAc,KAAK,EAAE;AAAA,EAC9B;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,OAAO;AAAA,EAAE;AAAA,EACT,OAAO;AACH,QAAI,CAAC,KAAK,WAAW,GAAG;AACpB,WAAK,SAAS;AACd,WAAK,iBAAiB;AAAA,IAC1B;AACA,SAAK,WAAW;AAAA,EACpB;AAAA;AAAA,EAEA,mBAAmB;AACf,mBAAe,MAAM,KAAK,UAAU,CAAC;AAAA,EACzC;AAAA,EACA,WAAW;AACP,SAAK,YAAY,QAAQ,CAAC,OAAO,GAAG,CAAC;AACrC,SAAK,cAAc,CAAC;AAAA,EACxB;AAAA,EACA,QAAQ;AAAA,EAAE;AAAA,EACV,UAAU;AAAA,EAAE;AAAA,EACZ,SAAS;AACL,SAAK,UAAU;AAAA,EACnB;AAAA,EACA,UAAU;AACN,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa;AAClB,UAAI,CAAC,KAAK,WAAW,GAAG;AACpB,aAAK,SAAS;AAAA,MAClB;AACA,WAAK,OAAO;AACZ,WAAK,cAAc,QAAQ,CAAC,OAAO,GAAG,CAAC;AACvC,WAAK,gBAAgB,CAAC;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,SAAK,WAAW;AAChB,SAAK,YAAY;AACjB,SAAK,cAAc,KAAK;AACxB,SAAK,aAAa,KAAK;AAAA,EAC3B;AAAA,EACA,YAAY,UAAU;AAClB,SAAK,YAAY,KAAK,YAAY,WAAW,KAAK,YAAY;AAAA,EAClE;AAAA,EACA,cAAc;AACV,WAAO,KAAK,YAAY,KAAK,YAAY,KAAK,YAAY;AAAA,EAC9D;AAAA;AAAA,EAEA,gBAAgB,WAAW;AACvB,UAAM,UAAU,aAAa,UAAU,KAAK,cAAc,KAAK;AAC/D,YAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC5B,YAAQ,SAAS;AAAA,EACrB;AACJ;AAUA,IAAM,uBAAN,MAA2B;AAAA,EACvB,aAAa,CAAC;AAAA,EACd,cAAc,CAAC;AAAA,EACf,YAAY;AAAA,EACZ,WAAW;AAAA,EACX,aAAa;AAAA,EACb,gBAAgB,CAAC;AAAA,EACjB,eAAe;AAAA,EACf,YAAY;AAAA,EACZ;AAAA,EACA,YAAY,UAAU;AAClB,SAAK,UAAU;AACf,QAAI,YAAY;AAChB,QAAI,eAAe;AACnB,QAAI,aAAa;AACjB,UAAM,QAAQ,KAAK,QAAQ;AAC3B,QAAI,SAAS,GAAG;AACZ,qBAAe,MAAM,KAAK,UAAU,CAAC;AAAA,IACzC,OACK;AACD,WAAK,QAAQ,QAAQ,CAAC,WAAW;AAC7B,eAAO,OAAO,MAAM;AAChB,cAAI,EAAE,aAAa,OAAO;AACtB,iBAAK,UAAU;AAAA,UACnB;AAAA,QACJ,CAAC;AACD,eAAO,UAAU,MAAM;AACnB,cAAI,EAAE,gBAAgB,OAAO;AACzB,iBAAK,WAAW;AAAA,UACpB;AAAA,QACJ,CAAC;AACD,eAAO,QAAQ,MAAM;AACjB,cAAI,EAAE,cAAc,OAAO;AACvB,iBAAK,SAAS;AAAA,UAClB;AAAA,QACJ,CAAC;AAAA,MACL,CAAC;AAAA,IACL;AACA,SAAK,YAAY,KAAK,QAAQ,OAAO,CAAC,MAAM,WAAW,KAAK,IAAI,MAAM,OAAO,SAAS,GAAG,CAAC;AAAA,EAC9F;AAAA,EACA,YAAY;AACR,QAAI,CAAC,KAAK,WAAW;AACjB,WAAK,YAAY;AACjB,WAAK,WAAW,QAAQ,CAAC,OAAO,GAAG,CAAC;AACpC,WAAK,aAAa,CAAC;AAAA,IACvB;AAAA,EACJ;AAAA,EACA,OAAO;AACH,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,KAAK,CAAC;AAAA,EAClD;AAAA,EACA,QAAQ,IAAI;AACR,SAAK,YAAY,KAAK,EAAE;AAAA,EAC5B;AAAA,EACA,WAAW;AACP,QAAI,CAAC,KAAK,WAAW,GAAG;AACpB,WAAK,WAAW;AAChB,WAAK,YAAY,QAAQ,CAAC,OAAO,GAAG,CAAC;AACrC,WAAK,cAAc,CAAC;AAAA,IACxB;AAAA,EACJ;AAAA,EACA,OAAO,IAAI;AACP,SAAK,WAAW,KAAK,EAAE;AAAA,EAC3B;AAAA,EACA,UAAU,IAAI;AACV,SAAK,cAAc,KAAK,EAAE;AAAA,EAC9B;AAAA,EACA,aAAa;AACT,WAAO,KAAK;AAAA,EAChB;AAAA,EACA,OAAO;AACH,QAAI,CAAC,KAAK,cAAc;AACpB,WAAK,KAAK;AAAA,IACd;AACA,SAAK,SAAS;AACd,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,KAAK,CAAC;AAAA,EAClD;AAAA,EACA,QAAQ;AACJ,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,UAAU;AACN,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC;AAAA,EACrD;AAAA,EACA,SAAS;AACL,SAAK,UAAU;AACf,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,OAAO,CAAC;AAAA,EACpD;AAAA,EACA,UAAU;AACN,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,aAAa;AACT,QAAI,CAAC,KAAK,YAAY;AAClB,WAAK,aAAa;AAClB,WAAK,UAAU;AACf,WAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,QAAQ,CAAC;AACjD,WAAK,cAAc,QAAQ,CAAC,OAAO,GAAG,CAAC;AACvC,WAAK,gBAAgB,CAAC;AAAA,IAC1B;AAAA,EACJ;AAAA,EACA,QAAQ;AACJ,SAAK,QAAQ,QAAQ,CAAC,WAAW,OAAO,MAAM,CAAC;AAC/C,SAAK,aAAa;AAClB,SAAK,YAAY;AACjB,SAAK,WAAW;AAAA,EACpB;AAAA,EACA,YAAY,GAAG;AACX,UAAM,iBAAiB,IAAI,KAAK;AAChC,SAAK,QAAQ,QAAQ,CAAC,WAAW;AAC7B,YAAM,WAAW,OAAO,YAAY,KAAK,IAAI,GAAG,iBAAiB,OAAO,SAAS,IAAI;AACrF,aAAO,YAAY,QAAQ;AAAA,IAC/B,CAAC;AAAA,EACL;AAAA,EACA,cAAc;AACV,UAAM,gBAAgB,KAAK,QAAQ,OAAO,CAAC,cAAc,WAAW;AAChE,YAAM,qBAAqB,iBAAiB,QAAQ,OAAO,YAAY,aAAa;AACpF,aAAO,qBAAqB,SAAS;AAAA,IACzC,GAAG,IAAI;AACP,WAAO,iBAAiB,OAAO,cAAc,YAAY,IAAI;AAAA,EACjE;AAAA,EACA,gBAAgB;AACZ,SAAK,QAAQ,QAAQ,CAAC,WAAW;AAC7B,UAAI,OAAO,eAAe;AACtB,eAAO,cAAc;AAAA,MACzB;AAAA,IACJ,CAAC;AAAA,EACL;AAAA;AAAA,EAEA,gBAAgB,WAAW;AACvB,UAAM,UAAU,aAAa,UAAU,KAAK,cAAc,KAAK;AAC/D,YAAQ,QAAQ,CAAC,OAAO,GAAG,CAAC;AAC5B,YAAQ,SAAS;AAAA,EACrB;AACJ;AAEA,IAAM,aAAa;", "names": ["AnimationMetadataType", "animation"]}