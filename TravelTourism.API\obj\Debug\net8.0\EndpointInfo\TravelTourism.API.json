{"openapi": "3.0.1", "info": {"title": "Travel & Tourism API", "description": "A comprehensive API for travel and tourism management", "contact": {"name": "Travel & Tourism Team", "email": "<EMAIL>"}, "version": "v1"}, "paths": {"/api/v1/admin/health": {"get": {"tags": ["Admin"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs": {"get": {"tags": ["AdminBlogs"], "summary": "Get all blogs with pagination and filtering", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Author", "in": "query", "schema": {"type": "string"}}, {"name": "IsPublished", "in": "query", "schema": {"type": "boolean"}}, {"name": "PublishedDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PublishedDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["AdminBlogs"], "summary": "Create a new blog", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Blog creation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}": {"get": {"tags": ["AdminBlogs"], "summary": "Get blog by ID", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["AdminBlogs"], "summary": "Update blog details", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update blog request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBlogRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminBlogs"], "summary": "Delete blog", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/publish": {"post": {"tags": ["AdminBlogs"], "summary": "Publish blog", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/unpublish": {"post": {"tags": ["AdminBlogs"], "summary": "Unpublish blog", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/feature": {"post": {"tags": ["AdminBlogs"], "summary": "Feature blog", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/unfeature": {"post": {"tags": ["AdminBlogs"], "summary": "Unfeature blog", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/statistics": {"get": {"tags": ["AdminBlogs"], "summary": "Get blog statistics", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/cover-image": {"post": {"tags": ["AdminBlogs"], "summary": "Upload blog cover image", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["file"], "type": "object", "properties": {"file": {"type": "string", "format": "binary"}}}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminBlogs"], "summary": "Delete blog cover image", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/comments": {"get": {"tags": ["AdminBlogs"], "summary": "Get blog comments", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/comments/{commentId}/approve": {"post": {"tags": ["AdminBlogs"], "summary": "Approve blog comment", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "commentId", "in": "path", "description": "Comment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/comments/{commentId}/reject": {"post": {"tags": ["AdminBlogs"], "summary": "Reject blog comment", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "commentId", "in": "path", "description": "Comment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/{id}/comments/{commentId}": {"delete": {"tags": ["AdminBlogs"], "summary": "Delete blog comment", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "commentId", "in": "path", "description": "Comment ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/categories": {"get": {"tags": ["AdminBlogs"], "summary": "Get blog categories", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["AdminBlogs"], "summary": "Create blog category", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Category creation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogCategoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogCategoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogCategoryRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/categories/{id}": {"put": {"tags": ["AdminBlogs"], "summary": "Update blog category", "parameters": [{"name": "id", "in": "path", "description": "Category ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update category request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogCategoryRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogCategoryRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBlogCategoryRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminBlogs"], "summary": "Delete blog category", "parameters": [{"name": "id", "in": "path", "description": "Category ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/tags": {"get": {"tags": ["AdminBlogs"], "summary": "Get blog tags", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["AdminBlogs"], "summary": "Create blog tag", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Tag creation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBlogTagRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBlogTagRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBlogTagRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/tags/{id}": {"put": {"tags": ["AdminBlogs"], "summary": "Update blog tag", "parameters": [{"name": "id", "in": "path", "description": "Tag ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update tag request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogTagRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBlogTagRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBlogTagRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminBlogs"], "summary": "Delete blog tag", "parameters": [{"name": "id", "in": "path", "description": "Tag ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/bulk-update": {"post": {"tags": ["AdminBlogs"], "summary": "Bulk update blog status", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Bulk update request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateBlogsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateBlogsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateBlogsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBlogs/export": {"get": {"tags": ["AdminBlogs"], "summary": "Export blogs to CSV", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Author", "in": "query", "schema": {"type": "string"}}, {"name": "IsPublished", "in": "query", "schema": {"type": "boolean"}}, {"name": "PublishedDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "PublishedDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Fields", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings": {"get": {"tags": ["AdminBookings"], "summary": "Get all bookings with pagination and filtering", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "BookingDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookingDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "TripDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "TripDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "MinAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxAmount", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}": {"get": {"tags": ["AdminBookings"], "summary": "Get booking by ID", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["AdminBookings"], "summary": "Update booking details", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update booking request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBookingRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminBookings"], "summary": "Delete booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/cancel": {"post": {"tags": ["AdminBookings"], "summary": "Cancel booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Cancel booking request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CancelBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CancelBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CancelBookingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/confirm": {"post": {"tags": ["AdminBookings"], "summary": "Confirm booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/refund": {"post": {"tags": ["AdminBookings"], "summary": "Process booking refund", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Refund request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessRefundRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProcessRefundRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ProcessRefundRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/status": {"put": {"tags": ["AdminBookings"], "summary": "Update booking status", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Status update request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingStatusRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingStatusRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBookingStatusRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/statistics": {"get": {"tags": ["AdminBookings"], "summary": "Get booking statistics", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/revenue-statistics": {"get": {"tags": ["AdminBookings"], "summary": "Get booking revenue statistics", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GroupBy", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/analytics": {"get": {"tags": ["AdminBookings"], "summary": "Get booking analytics", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GroupBy", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/reports": {"get": {"tags": ["AdminBookings"], "summary": "Get booking reports", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ReportType", "in": "query", "schema": {"type": "string"}}, {"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/export": {"get": {"tags": ["AdminBookings"], "summary": "Export bookings to CSV", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "BookingDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookingDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "TripDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "TripDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Fields", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/export-invoices": {"get": {"tags": ["AdminBookings"], "summary": "Export booking invoices to PDF", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "BookingId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "UserId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "IncludePaid", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludeUnpaid", "in": "query", "schema": {"type": "boolean"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/reminder": {"post": {"tags": ["AdminBookings"], "summary": "Send booking reminder", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Reminder request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendReminderRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendReminderRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendReminderRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/payment": {"get": {"tags": ["AdminBookings"], "summary": "Get booking payment details", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/customer": {"get": {"tags": ["AdminBookings"], "summary": "Get booking customer details", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/trip": {"get": {"tags": ["AdminBookings"], "summary": "Get booking trip details", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/notes": {"post": {"tags": ["AdminBookings"], "summary": "Add booking note", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Note request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddBookingNoteRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AddBookingNoteRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AddBookingNoteRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "get": {"tags": ["AdminBookings"], "summary": "Get booking notes", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/notes/{noteId}": {"put": {"tags": ["AdminBookings"], "summary": "Update booking note", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "noteId", "in": "path", "description": "Note ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update note request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingNoteRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingNoteRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBookingNoteRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminBookings"], "summary": "Delete booking note", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "noteId", "in": "path", "description": "Note ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/bulk-update": {"post": {"tags": ["AdminBookings"], "summary": "Bulk update bookings", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Bulk update request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateBookingsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateBookingsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateBookingsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminBookings/{id}/activity": {"get": {"tags": ["AdminBookings"], "summary": "Get booking activity log", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/overview": {"get": {"tags": ["AdminDashboard"], "summary": "Get dashboard overview statistics", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/statistics": {"get": {"tags": ["AdminDashboard"], "summary": "Get system statistics", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/revenue-analytics": {"get": {"tags": ["AdminDashboard"], "summary": "Get revenue analytics", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GroupBy", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PaymentMethod", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/user-analytics": {"get": {"tags": ["AdminDashboard"], "summary": "Get user analytics", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GroupBy", "in": "query", "schema": {"type": "string"}}, {"name": "Role", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/booking-trends": {"get": {"tags": ["AdminDashboard"], "summary": "Get booking trends", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "GroupBy", "in": "query", "schema": {"type": "string"}}, {"name": "TripId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/popular-destinations": {"get": {"tags": ["AdminDashboard"], "summary": "Get popular destinations", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/recent-activities": {"get": {"tags": ["AdminDashboard"], "summary": "Get recent activities", "parameters": [{"name": "Limit", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ActivityType", "in": "query", "schema": {"type": "string"}}, {"name": "Since", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/performance-metrics": {"get": {"tags": ["AdminDashboard"], "summary": "Get performance metrics", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "MetricType", "in": "query", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/health-status": {"get": {"tags": ["AdminDashboard"], "summary": "Get system health status", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/audit-logs": {"get": {"tags": ["AdminDashboard"], "summary": "Get audit logs", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "UserId", "in": "query", "schema": {"type": "string"}}, {"name": "Action", "in": "query", "schema": {"type": "string"}}, {"name": "EntityType", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/configurations": {"get": {"tags": ["AdminDashboard"], "summary": "Get system configurations", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["AdminDashboard"], "summary": "Update system configuration", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update configuration request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateConfigurationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateConfigurationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateConfigurationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/database-statistics": {"get": {"tags": ["AdminDashboard"], "summary": "Get database statistics", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/server-metrics": {"get": {"tags": ["AdminDashboard"], "summary": "Get server metrics", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/application-logs": {"get": {"tags": ["AdminDashboard"], "summary": "Get application logs", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Level", "in": "query", "schema": {"type": "string"}}, {"name": "Source", "in": "query", "schema": {"type": "string"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/error-logs": {"get": {"tags": ["AdminDashboard"], "summary": "Get error logs", "parameters": [{"name": "StartDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "EndDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ErrorType", "in": "query", "schema": {"type": "string"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/clear-cache": {"post": {"tags": ["AdminDashboard"], "summary": "Clear application cache", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/send-notification": {"post": {"tags": ["AdminDashboard"], "summary": "Send system notification", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "System notification request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSystemNotificationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendSystemNotificationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendSystemNotificationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/export-data": {"post": {"tags": ["AdminDashboard"], "summary": "Export system data", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Export data request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ExportSystemDataRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ExportSystemDataRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ExportSystemDataRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/backup-status": {"get": {"tags": ["AdminDashboard"], "summary": "Get backup status", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/create-backup": {"post": {"tags": ["AdminDashboard"], "summary": "Create system backup", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Backup request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBackupRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBackupRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBackupRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/maintenance-mode": {"get": {"tags": ["AdminDashboard"], "summary": "Get maintenance mode status", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminDashboard/toggle-maintenance-mode": {"post": {"tags": ["AdminDashboard"], "summary": "Toggle maintenance mode", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Toggle maintenance mode request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ToggleMaintenanceModeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ToggleMaintenanceModeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ToggleMaintenanceModeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips": {"get": {"tags": ["AdminTrips"], "summary": "Get all trips with pagination and filtering", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MinPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "StartDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "StartDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["AdminTrips"], "summary": "Create a new trip", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Trip creation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTripRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateTripRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateTripRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}": {"get": {"tags": ["AdminTrips"], "summary": "Get trip by ID", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["AdminTrips"], "summary": "Update trip details", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update trip request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTripRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateTripRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateTripRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminTrips"], "summary": "Delete trip", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/publish": {"post": {"tags": ["AdminTrips"], "summary": "Publish trip", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/unpublish": {"post": {"tags": ["AdminTrips"], "summary": "Unpublish trip", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/feature": {"post": {"tags": ["AdminTrips"], "summary": "Feature trip", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/unfeature": {"post": {"tags": ["AdminTrips"], "summary": "Unfeature trip", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/bookings": {"get": {"tags": ["AdminTrips"], "summary": "Get trip bookings", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/statistics": {"get": {"tags": ["AdminTrips"], "summary": "Get trip statistics", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/images": {"post": {"tags": ["AdminTrips"], "summary": "Upload trip images", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["files"], "type": "object", "properties": {"files": {"type": "array", "items": {"type": "string", "format": "binary"}}}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/images/{imageId}": {"delete": {"tags": ["AdminTrips"], "summary": "Delete trip image", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "imageId", "in": "path", "description": "Image ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/duplicate": {"post": {"tags": ["AdminTrips"], "summary": "Duplicate trip", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/{id}/reviews": {"get": {"tags": ["AdminTrips"], "summary": "Get trip reviews", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/bulk-update": {"post": {"tags": ["AdminTrips"], "summary": "Bulk update trip status", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Bulk update request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateTripsRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BulkUpdateTripsRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/BulkUpdateTripsRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminTrips/export": {"get": {"tags": ["AdminTrips"], "summary": "Export trips to CSV", "parameters": [{"name": "Format", "in": "query", "schema": {"type": "string"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "CityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "StartDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "StartDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Fields", "in": "query", "schema": {"type": "array", "items": {"type": "string"}}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers": {"get": {"tags": ["AdminUsers"], "summary": "Get all users with pagination and filtering", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Role", "in": "query", "schema": {"type": "string"}}, {"name": "IsActive", "in": "query", "schema": {"type": "boolean"}}, {"name": "CreatedDateFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "CreatedDateTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDescending", "in": "query", "schema": {"type": "boolean"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["AdminUsers"], "summary": "Create a new user", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "User creation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}": {"get": {"tags": ["AdminUsers"], "summary": "Get user by ID", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["AdminUsers"], "summary": "Update user details", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update user request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["AdminUsers"], "summary": "Delete user", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/activate": {"post": {"tags": ["AdminUsers"], "summary": "Activate user account", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/deactivate": {"post": {"tags": ["AdminUsers"], "summary": "Deactivate user account", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/roles": {"post": {"tags": ["AdminUsers"], "summary": "Assign role to user", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Role assignment request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignRoleRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AssignRoleRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AssignRoleRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/roles/{roleName}": {"delete": {"tags": ["AdminUsers"], "summary": "Remove role from user", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "path", "description": "Role name", "required": true, "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/bookings": {"get": {"tags": ["AdminUsers"], "summary": "Get user's bookings", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/statistics": {"get": {"tags": ["AdminUsers"], "summary": "Get user statistics", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/notifications": {"post": {"tags": ["AdminUsers"], "summary": "Send notification to user", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Notification request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/SendNotificationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/admin/AdminUsers/{id}/reset-password": {"post": {"tags": ["AdminUsers"], "summary": "Reset user password", "parameters": [{"name": "id", "in": "path", "description": "User ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Register a new user account", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "User registration information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RegisterDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}}}}}}, "/api/v1/Auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Login with email and password", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Login credentials", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}}}}}}, "/api/v1/Auth/verify-email": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Verify email address", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Email verification details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyEmailDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/VerifyEmailDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/VerifyEmailDto"}}}}, "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Auth/forgot-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Request password reset", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Forgot password request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ForgotPasswordDto"}}}}, "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Auth/reset-password": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Reset password with token", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Password reset details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordDto"}}}}, "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Auth/refresh-token": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Refresh JWT access token", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Refresh token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefreshTokenDto"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "application/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthResultDto"}}}}}}}, "/api/v1/Auth/logout": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Logout current user (revoke all tokens)", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Auth/revoke-all-tokens": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Revoke all user tokens (force logout from all devices)", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "401": {"description": "Unauthorized", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Blogs": {"get": {"tags": ["Blogs"], "summary": "Get all blogs with filtering and pagination", "parameters": [{"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Items per page", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "search", "in": "query", "description": "Search term", "schema": {"type": "string"}}, {"name": "category", "in": "query", "description": "Filter by category", "schema": {"type": "string"}}, {"name": "sortBy", "in": "query", "description": "Sort field", "schema": {"type": "string", "default": "CreatedAt"}}, {"name": "sortDirection", "in": "query", "description": "Sort direction", "schema": {"type": "string", "default": "desc"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Blogs/{id}": {"get": {"tags": ["Blogs"], "summary": "Get blog by ID", "parameters": [{"name": "id", "in": "path", "description": "Blog ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Blogs/categories": {"get": {"tags": ["Blogs"], "summary": "Get all blog categories (public endpoint)", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Blogs/featured": {"get": {"tags": ["Blogs"], "summary": "Get featured blogs", "parameters": [{"name": "count", "in": "query", "description": "Number of featured blogs to return", "schema": {"type": "integer", "format": "int32", "default": 5}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Blogs/category/{categoryId}": {"get": {"tags": ["Blogs"], "summary": "Get blogs by category", "parameters": [{"name": "categoryId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "page", "in": "query", "description": "Page number", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "description": "Items per page", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Blogs/{id}/related": {"get": {"tags": ["Blogs"], "summary": "Get related blogs", "parameters": [{"name": "id", "in": "path", "description": "Blog ID to find related blogs for", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "count", "in": "query", "description": "Number of related blogs to return", "schema": {"type": "integer", "format": "int32", "default": 5}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings": {"post": {"tags": ["Bookings"], "summary": "Create a new booking", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Booking creation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CreateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/CreateBookingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings/{id}": {"get": {"tags": ["Bookings"], "summary": "Get booking by ID", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Bookings"], "summary": "Cancel a booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Bookings"], "summary": "Update booking details", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Update booking request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateBookingRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateBookingRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings/{id}/payment": {"post": {"tags": ["Bookings"], "summary": "Process payment for a booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Payment request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings/{id}/payment/status": {"get": {"tags": ["Bookings"], "summary": "Get payment status for a booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "paymentIntentId", "in": "query", "description": "Payment Intent ID", "schema": {"type": "string"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings/{id}/payment/confirm": {"post": {"tags": ["Bookings"], "summary": "Confirm payment for a booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Payment confirmation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PaymentConfirmationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/PaymentConfirmationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/PaymentConfirmationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings/{id}/refund": {"post": {"tags": ["Bookings"], "summary": "Request refund for a booking", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"description": "Refund request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefundRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RefundRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/RefundRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/v1/Bookings/{id}/invoice": {"get": {"tags": ["Bookings"], "summary": "Get booking invoice", "parameters": [{"name": "id", "in": "path", "description": "Booking ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Cities": {"get": {"tags": ["Cities"], "parameters": [{"name": "countryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Cities/{id}": {"get": {"tags": ["Cities"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Countries": {"get": {"tags": ["Countries"], "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Countries/{id}": {"get": {"tags": ["Countries"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/v1/Files/upload-image": {"post": {"tags": ["Files"], "summary": "Upload an image file", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["File"], "type": "object", "properties": {"File": {"type": "string", "format": "binary"}, "Folder": {"type": "string"}}}, "encoding": {"File": {"style": "form"}, "Folder": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v1/Files/upload-document": {"post": {"tags": ["Files"], "summary": "Upload a document file", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"required": ["File"], "type": "object", "properties": {"File": {"type": "string", "format": "binary"}, "Folder": {"type": "string"}}}, "encoding": {"File": {"style": "form"}, "Folder": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ObjectApiResponse"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ApiResponse"}}}}}}}, "/api/v1/Trips": {"get": {"tags": ["Trips"], "summary": "Get paginated list of trips with optional filtering", "parameters": [{"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "CategoryId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DestinationId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DestinationCityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "DepartureCityId", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "query", "schema": {"$ref": "#/components/schemas/TripDifficulty"}}, {"name": "DifficultyLevel", "in": "query", "schema": {"$ref": "#/components/schemas/TripDifficulty"}}, {"name": "MinPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MaxPrice", "in": "query", "schema": {"type": "number", "format": "double"}}, {"name": "MinDuration", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "MaxDuration", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "AvailableFrom", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "AvailableTo", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "IncludesAccommodation", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludesTransport", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludesMeals", "in": "query", "schema": {"type": "boolean"}}, {"name": "IncludesGuide", "in": "query", "schema": {"type": "boolean"}}, {"name": "IsFeatured", "in": "query", "schema": {"type": "boolean"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortOrder", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "<PERSON><PERSON>", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "Take", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripDtoPagedResultApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripDtoPagedResultApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripDtoPagedResultApiResponse"}}}}}}}, "/api/v1/Trips/{id}": {"get": {"tags": ["Trips"], "summary": "Get trip details by ID", "parameters": [{"name": "id", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripDetailDtoApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripDetailDtoApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripDetailDtoApiResponse"}}}}, "404": {"description": "Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ProblemDetails"}}}}}}}, "/api/v1/Trips/featured": {"get": {"tags": ["Trips"], "summary": "Get featured trips", "parameters": [{"name": "count", "in": "query", "description": "Number of featured trips to return (default: 10)", "schema": {"type": "integer", "format": "int32", "default": 10}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}}}}}}, "/api/v1/Trips/categories": {"get": {"tags": ["Trips"], "summary": "Get all trip categories", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripCategoryDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripCategoryDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripCategoryDtoListApiResponse"}}}}}}}, "/api/v1/Trips/destinations": {"get": {"tags": ["Trips"], "summary": "Get all available destinations", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/CityDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/CityDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/CityDtoListApiResponse"}}}}}}}, "/api/v1/Trips/category/{categoryId}": {"get": {"tags": ["Trips"], "summary": "Get trips by category", "parameters": [{"name": "categoryId", "in": "path", "description": "Category ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}}}}}}, "/api/v1/Trips/destination/{destinationId}": {"get": {"tags": ["Trips"], "summary": "Get trips by destination", "parameters": [{"name": "destinationId", "in": "path", "description": "Destination city ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}}}}}}, "/api/v1/Trips/search": {"get": {"tags": ["Trips"], "summary": "Search trips", "parameters": [{"name": "searchTerm", "in": "query", "description": "Search term", "schema": {"type": "string"}}, {"name": "categoryId", "in": "query", "description": "Optional category filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "destinationId", "in": "query", "description": "Optional destination filter", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TripDtoListApiResponse"}}}}}}}, "/api/v1/Trips/{tripId}/availability": {"get": {"tags": ["Trips"], "summary": "Check if a trip is available for booking", "parameters": [{"name": "tripId", "in": "path", "description": "Trip ID", "required": true, "schema": {"type": "integer", "format": "int32"}}, {"name": "travelDate", "in": "query", "description": "Travel date", "schema": {"type": "string", "format": "date-time"}}, {"name": "numberOfPeople", "in": "query", "description": "Number of people", "schema": {"type": "integer", "format": "int32"}}, {"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/BooleanApiResponse"}}}}}}}, "/api/v1/Users/<USER>": {"delete": {"tags": ["Users"], "summary": "Delete current user's account", "parameters": [{"name": "api-version", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}}, "components": {"schemas": {"AddBookingNoteRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "note": {"type": "string", "nullable": true}, "noteType": {"type": "string", "nullable": true}, "isVisibleToCustomer": {"type": "boolean"}}, "additionalProperties": false}, "ApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AssignRoleRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "role": {"type": "string", "nullable": true}}, "additionalProperties": false}, "AuthResultDto": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "token": {"$ref": "#/components/schemas/TokenDto"}, "user": {"$ref": "#/components/schemas/UserDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "BookingStatus": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "BooleanApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "boolean"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "BulkUpdateBlogsRequest": {"type": "object", "properties": {"blogIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "isPublished": {"type": "boolean", "nullable": true}, "categoryId": {"type": "integer", "format": "int32", "nullable": true}, "author": {"type": "string", "nullable": true}, "publishedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BulkUpdateBookingsRequest": {"type": "object", "properties": {"bookingIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "processedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "BulkUpdateTripsRequest": {"type": "object", "properties": {"tripIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "isFeatured": {"type": "boolean", "nullable": true}, "categoryId": {"type": "integer", "format": "int32", "nullable": true}, "priceAdjustment": {"type": "number", "format": "double", "nullable": true}, "priceAdjustmentType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CancelBookingRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "reason": {"type": "string", "nullable": true}, "processRefund": {"type": "boolean"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CityDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "countryId": {"type": "integer", "format": "int32"}, "countryName": {"type": "string", "nullable": true}, "countryCode": {"type": "string", "nullable": true}, "timeZone": {"type": "string", "nullable": true}, "latitude": {"type": "number", "format": "double", "nullable": true}, "longitude": {"type": "number", "format": "double", "nullable": true}, "isActive": {"type": "boolean"}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "CityDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/CityDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CreateBackupRequest": {"type": "object", "properties": {"backupType": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "includeFiles": {"type": "boolean"}, "includeDatabase": {"type": "boolean"}, "includeConfiguration": {"type": "boolean"}}, "additionalProperties": false}, "CreateBlogCategoryRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "iconUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateBlogRequest": {"required": ["categoryId", "content", "excerpt", "title"], "type": "object", "properties": {"title": {"maxLength": 200, "minLength": 1, "type": "string"}, "content": {"minLength": 1, "type": "string"}, "excerpt": {"maxLength": 500, "minLength": 1, "type": "string"}, "categoryId": {"type": "integer", "format": "int32"}, "featuredImageUrl": {"type": "string", "nullable": true}, "isPublished": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "CreateBlogTagRequest": {"type": "object", "properties": {"name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "CreateBookingRequest": {"required": ["emergencyContactName", "emergencyContactPhone", "numberOfAdults", "pricePer<PERSON><PERSON>", "travelDate", "tripId"], "type": "object", "properties": {"tripId": {"type": "integer", "format": "int32"}, "travelDate": {"type": "string", "format": "date-time"}, "numberOfAdults": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}, "numberOfChildren": {"maximum": 100, "minimum": 0, "type": "integer", "format": "int32"}, "numberOfInfants": {"maximum": 100, "minimum": 0, "type": "integer", "format": "int32"}, "pricePerPerson": {"minimum": 0, "type": "number", "format": "double"}, "specialRequests": {"maxLength": 1000, "type": "string", "nullable": true}, "emergencyContactName": {"maxLength": 100, "minLength": 1, "type": "string"}, "emergencyContactPhone": {"maxLength": 20, "minLength": 1, "type": "string"}, "totalPeople": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "CreateTripRequest": {"required": ["availableFrom", "availableTo", "categoryId", "departureCityId", "description", "destinationCityId", "difficulty", "duration", "mainImageUrl", "maxCapacity", "name", "price", "shortDescription"], "type": "object", "properties": {"name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"minLength": 1, "type": "string"}, "shortDescription": {"maxLength": 500, "minLength": 1, "type": "string"}, "price": {"minimum": 0, "type": "number", "format": "double"}, "discountPrice": {"minimum": 0, "type": "number", "format": "double", "nullable": true}, "duration": {"maximum": 365, "minimum": 1, "type": "integer", "format": "int32"}, "maxCapacity": {"maximum": 1000, "minimum": 1, "type": "integer", "format": "int32"}, "minAge": {"maximum": 120, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}, "maxAge": {"maximum": 120, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}, "difficulty": {"$ref": "#/components/schemas/TripDifficulty"}, "categoryId": {"type": "integer", "format": "int32"}, "destinationCityId": {"type": "integer", "format": "int32"}, "departureCityId": {"type": "integer", "format": "int32"}, "includesAccommodation": {"type": "boolean"}, "includesTransport": {"type": "boolean"}, "includesMeals": {"type": "boolean"}, "includesGuide": {"type": "boolean"}, "mainImageUrl": {"maxLength": 500, "minLength": 1, "type": "string"}, "isFeatured": {"type": "boolean"}, "availableFrom": {"type": "string", "format": "date-time"}, "availableTo": {"type": "string", "format": "date-time"}, "imageUrls": {"type": "array", "items": {"type": "string"}, "nullable": true}, "itineraries": {"type": "array", "items": {"$ref": "#/components/schemas/TripItineraryDto"}, "nullable": true}}, "additionalProperties": false}, "CreateUserRequest": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"type": "string", "nullable": true}, "addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ExportSystemDataRequest": {"type": "object", "properties": {"dataType": {"type": "string", "nullable": true}, "format": {"type": "string", "nullable": true}, "startDate": {"type": "string", "format": "date-time", "nullable": true}, "endDate": {"type": "string", "format": "date-time", "nullable": true}, "includeDeleted": {"type": "boolean"}}, "additionalProperties": false}, "ForgotPasswordDto": {"required": ["email"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}}, "additionalProperties": false}, "Gender": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "LoginDto": {"required": ["email", "password"], "type": "object", "properties": {"email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 6, "type": "string"}}, "additionalProperties": false}, "ObjectApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentConfirmationRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "paymentMethod": {"type": "string", "nullable": true}, "transactionId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "PaymentRequest": {"type": "object", "properties": {"bookingNumber": {"type": "string", "nullable": true}, "paymentMethodId": {"type": "string", "nullable": true}, "amount": {"type": "number", "format": "double"}, "currency": {"type": "string", "nullable": true}, "userId": {"type": "integer", "format": "int32"}, "tripId": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "ProblemDetails": {"type": "object", "properties": {"type": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}, "status": {"type": "integer", "format": "int32", "nullable": true}, "detail": {"type": "string", "nullable": true}, "instance": {"type": "string", "nullable": true}}, "additionalProperties": {}}, "ProcessRefundRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "refundAmount": {"type": "number", "format": "double"}, "reason": {"type": "string", "nullable": true}, "refundMethod": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RefreshTokenDto": {"type": "object", "properties": {"refreshToken": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RefundRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "refundAmount": {"type": "number", "format": "double"}, "reason": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}}, "additionalProperties": false}, "RegisterDto": {"required": ["addressLine1", "addressLine2", "city", "confirmPassword", "country", "email", "firstName", "lastName", "password", "postalCode", "state"], "type": "object", "properties": {"firstName": {"maxLength": 100, "minLength": 1, "type": "string"}, "lastName": {"maxLength": 100, "minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "password": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}, "phoneNumber": {"type": "string", "format": "tel", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"$ref": "#/components/schemas/Gender"}, "addressLine1": {"maxLength": 200, "minLength": 1, "type": "string"}, "addressLine2": {"maxLength": 200, "minLength": 1, "type": "string"}, "city": {"maxLength": 100, "minLength": 1, "type": "string"}, "state": {"maxLength": 100, "minLength": 1, "type": "string"}, "country": {"maxLength": 100, "minLength": 1, "type": "string"}, "postalCode": {"maxLength": 20, "minLength": 1, "type": "string"}}, "additionalProperties": false}, "ResetPasswordDto": {"required": ["confirmPassword", "email", "newPassword", "token"], "type": "object", "properties": {"token": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}, "newPassword": {"minLength": 6, "type": "string"}, "confirmPassword": {"minLength": 1, "type": "string"}}, "additionalProperties": false}, "SendNotificationRequest": {"type": "object", "properties": {"userId": {"type": "integer", "format": "int32"}, "subject": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}}, "additionalProperties": false}, "SendReminderRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "reminderType": {"type": "string", "nullable": true}, "subject": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "sendAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "SendSystemNotificationRequest": {"type": "object", "properties": {"title": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}, "type": {"type": "string", "nullable": true}, "targetAudience": {"type": "string", "nullable": true}, "userIds": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "expiresAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "ToggleMaintenanceModeRequest": {"type": "object", "properties": {"isMaintenanceMode": {"type": "boolean"}, "maintenanceMessage": {"type": "string", "nullable": true}, "scheduledEndTime": {"type": "string", "format": "date-time", "nullable": true}, "allowAdmins": {"type": "boolean"}}, "additionalProperties": false}, "TokenDto": {"type": "object", "properties": {"accessToken": {"type": "string", "nullable": true}, "refreshToken": {"type": "string", "nullable": true}, "expiresAt": {"type": "string", "format": "date-time"}, "tokenType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripCategoryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "iconUrl": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripCategoryDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TripCategoryDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripDetailDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "discountPrice": {"type": "number", "format": "double", "nullable": true}, "duration": {"type": "integer", "format": "int32"}, "maxCapacity": {"type": "integer", "format": "int32"}, "minAge": {"type": "integer", "format": "int32", "nullable": true}, "maxAge": {"type": "integer", "format": "int32", "nullable": true}, "difficulty": {"$ref": "#/components/schemas/TripDifficulty"}, "mainImageUrl": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "availableFrom": {"type": "string", "format": "date-time"}, "availableTo": {"type": "string", "format": "date-time"}, "category": {"$ref": "#/components/schemas/TripCategoryDto"}, "destinationCity": {"$ref": "#/components/schemas/CityDto"}, "departureCity": {"$ref": "#/components/schemas/CityDto"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/TripImageDto"}, "nullable": true}, "effectivePrice": {"type": "number", "format": "double", "readOnly": true}, "hasDiscount": {"type": "boolean", "readOnly": true}, "discountPercentage": {"type": "number", "format": "double", "readOnly": true}, "description": {"type": "string", "nullable": true}, "includesAccommodation": {"type": "boolean"}, "includesTransport": {"type": "boolean"}, "includesMeals": {"type": "boolean"}, "includesGuide": {"type": "boolean"}, "itineraries": {"type": "array", "items": {"$ref": "#/components/schemas/TripItineraryDto"}, "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}, "updatedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TripDetailDtoApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/TripDetailDto"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripDifficulty": {"enum": [1, 2, 3], "type": "integer", "format": "int32"}, "TripDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "shortDescription": {"type": "string", "nullable": true}, "price": {"type": "number", "format": "double"}, "discountPrice": {"type": "number", "format": "double", "nullable": true}, "duration": {"type": "integer", "format": "int32"}, "maxCapacity": {"type": "integer", "format": "int32"}, "minAge": {"type": "integer", "format": "int32", "nullable": true}, "maxAge": {"type": "integer", "format": "int32", "nullable": true}, "difficulty": {"$ref": "#/components/schemas/TripDifficulty"}, "mainImageUrl": {"type": "string", "nullable": true}, "isFeatured": {"type": "boolean"}, "availableFrom": {"type": "string", "format": "date-time"}, "availableTo": {"type": "string", "format": "date-time"}, "category": {"$ref": "#/components/schemas/TripCategoryDto"}, "destinationCity": {"$ref": "#/components/schemas/CityDto"}, "departureCity": {"$ref": "#/components/schemas/CityDto"}, "images": {"type": "array", "items": {"$ref": "#/components/schemas/TripImageDto"}, "nullable": true}, "effectivePrice": {"type": "number", "format": "double", "readOnly": true}, "hasDiscount": {"type": "boolean", "readOnly": true}, "discountPercentage": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "TripDtoListApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"type": "array", "items": {"$ref": "#/components/schemas/TripDto"}, "nullable": true}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripDtoPagedResult": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/components/schemas/TripDto"}, "nullable": true}, "totalCount": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "totalPages": {"type": "integer", "format": "int32"}, "hasPreviousPage": {"type": "boolean"}, "hasNextPage": {"type": "boolean"}}, "additionalProperties": false}, "TripDtoPagedResultApiResponse": {"type": "object", "properties": {"success": {"type": "boolean"}, "message": {"type": "string", "nullable": true}, "data": {"$ref": "#/components/schemas/TripDtoPagedResult"}, "errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "errorCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TripImageDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "imageUrl": {"type": "string", "nullable": true}, "caption": {"type": "string", "nullable": true}, "isMain": {"type": "boolean"}, "displayOrder": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "TripItineraryDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "day": {"type": "integer", "format": "int32"}, "title": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "activities": {"type": "string", "nullable": true}, "meals": {"type": "string", "nullable": true}, "accommodation": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateBlogCategoryRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "iconUrl": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateBlogRequest": {"required": ["categoryId", "content", "excerpt", "title"], "type": "object", "properties": {"title": {"maxLength": 200, "minLength": 1, "type": "string"}, "content": {"minLength": 1, "type": "string"}, "excerpt": {"maxLength": 500, "minLength": 1, "type": "string"}, "categoryId": {"type": "integer", "format": "int32"}, "featuredImageUrl": {"type": "string", "nullable": true}, "isPublished": {"type": "boolean"}, "isFeatured": {"type": "boolean"}, "tags": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "UpdateBlogTagRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "color": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "UpdateBookingNoteRequest": {"type": "object", "properties": {"noteId": {"type": "integer", "format": "int32"}, "note": {"type": "string", "nullable": true}, "noteType": {"type": "string", "nullable": true}, "isVisibleToCustomer": {"type": "boolean"}}, "additionalProperties": false}, "UpdateBookingRequest": {"required": ["emergencyContactName", "emergencyContactPhone", "numberOfAdults", "pricePer<PERSON><PERSON>", "travelDate", "tripId"], "type": "object", "properties": {"tripId": {"type": "integer", "format": "int32"}, "travelDate": {"type": "string", "format": "date-time"}, "numberOfAdults": {"maximum": 100, "minimum": 1, "type": "integer", "format": "int32"}, "numberOfChildren": {"maximum": 100, "minimum": 0, "type": "integer", "format": "int32"}, "numberOfInfants": {"maximum": 100, "minimum": 0, "type": "integer", "format": "int32"}, "pricePerPerson": {"minimum": 0, "type": "number", "format": "double"}, "specialRequests": {"maxLength": 1000, "type": "string", "nullable": true}, "emergencyContactName": {"maxLength": 100, "minLength": 1, "type": "string"}, "emergencyContactPhone": {"maxLength": 20, "minLength": 1, "type": "string"}, "totalPeople": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "UpdateBookingStatusRequest": {"type": "object", "properties": {"bookingId": {"type": "integer", "format": "int32"}, "status": {"type": "string", "nullable": true}, "notes": {"type": "string", "nullable": true}, "processedAt": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "UpdateConfigurationRequest": {"type": "object", "properties": {"key": {"type": "string", "nullable": true}, "value": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "category": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateTripRequest": {"required": ["availableFrom", "availableTo", "categoryId", "departureCityId", "description", "destinationCityId", "difficulty", "duration", "mainImageUrl", "maxCapacity", "name", "price", "shortDescription"], "type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "name": {"maxLength": 200, "minLength": 1, "type": "string"}, "description": {"minLength": 1, "type": "string"}, "shortDescription": {"maxLength": 500, "minLength": 1, "type": "string"}, "price": {"minimum": 0, "type": "number", "format": "double"}, "discountPrice": {"minimum": 0, "type": "number", "format": "double", "nullable": true}, "duration": {"maximum": 365, "minimum": 1, "type": "integer", "format": "int32"}, "maxCapacity": {"maximum": 1000, "minimum": 1, "type": "integer", "format": "int32"}, "minAge": {"maximum": 120, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}, "maxAge": {"maximum": 120, "minimum": 0, "type": "integer", "format": "int32", "nullable": true}, "difficulty": {"$ref": "#/components/schemas/TripDifficulty"}, "categoryId": {"type": "integer", "format": "int32"}, "destinationCityId": {"type": "integer", "format": "int32"}, "departureCityId": {"type": "integer", "format": "int32"}, "includesAccommodation": {"type": "boolean"}, "includesTransport": {"type": "boolean"}, "includesMeals": {"type": "boolean"}, "includesGuide": {"type": "boolean"}, "mainImageUrl": {"maxLength": 500, "minLength": 1, "type": "string"}, "isFeatured": {"type": "boolean"}, "availableFrom": {"type": "string", "format": "date-time"}, "availableTo": {"type": "string", "format": "date-time"}, "imageUrls": {"type": "array", "items": {"type": "string"}, "nullable": true}, "itineraries": {"type": "array", "items": {"$ref": "#/components/schemas/TripItineraryDto"}, "nullable": true}}, "additionalProperties": false}, "UpdateUserDto": {"type": "object", "properties": {"firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "profileImageUrl": {"type": "string", "nullable": true}, "addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UpdateUserRequest": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "role": {"type": "string", "nullable": true}, "isActive": {"type": "boolean", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"type": "string", "nullable": true}, "addressLine1": {"type": "string", "nullable": true}, "addressLine2": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "country": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserDto": {"type": "object", "properties": {"id": {"type": "integer", "format": "int32"}, "firstName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "phoneNumber": {"type": "string", "nullable": true}, "dateOfBirth": {"type": "string", "format": "date-time", "nullable": true}, "gender": {"$ref": "#/components/schemas/Gender"}, "profileImageUrl": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/UserRole"}, "isEmailVerified": {"type": "boolean"}, "lastLoginAt": {"type": "string", "format": "date-time", "nullable": true}, "createdAt": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserRole": {"enum": [1, 2], "type": "integer", "format": "int32"}, "VerifyEmailDto": {"required": ["email", "token"], "type": "object", "properties": {"token": {"minLength": 1, "type": "string"}, "email": {"minLength": 1, "type": "string", "format": "email"}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the <PERSON><PERSON> scheme. Example: \"Authorization: Bearer {token}\"", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}