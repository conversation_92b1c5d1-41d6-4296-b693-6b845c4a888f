[2025-07-11 00:08:27.694 +03:00 WRN] Entity 'Blog' has a global query filter defined and is the required end of a relationship with the entity 'BlogImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:27.794 +03:00 WRN] Entity 'Booking' has a global query filter defined and is the required end of a relationship with the entity 'BookingPayment'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:27.805 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:27.810 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripItinerary'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:27.824 +03:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:27.985 +03:00 WRN] The 'UserRole' property 'Role' on entity type 'User' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'UserRole' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-07-11 00:08:28.037 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Currency'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
[2025-07-11 00:08:29.025 +03:00 WRN] Entity 'Blog' has a global query filter defined and is the required end of a relationship with the entity 'BlogImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:29.152 +03:00 WRN] Entity 'Booking' has a global query filter defined and is the required end of a relationship with the entity 'BookingPayment'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:29.164 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripImage'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:29.172 +03:00 WRN] Entity 'Trip' has a global query filter defined and is the required end of a relationship with the entity 'TripItinerary'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:29.178 +03:00 WRN] Entity 'User' has a global query filter defined and is the required end of a relationship with the entity 'UserToken'. This may lead to unexpected results when the required entity is filtered out. Either configure the navigation as optional, or define matching query filters for both entities in the navigation. See https://go.microsoft.com/fwlink/?linkid=2131316 for more information.
[2025-07-11 00:08:29.210 +03:00 WRN] The 'UserRole' property 'Role' on entity type 'User' is configured with a database-generated default, but has no configured sentinel value. The database-generated default will always be used for inserts when the property has the value '0', since this is the CLR default for the 'UserRole' type. Consider using a nullable type, using a nullable backing field, or setting the sentinel value for the property to ensure the database default is used when, and only when, appropriate. See https://aka.ms/efcore-docs-default-values for more information.
[2025-07-11 00:08:29.286 +03:00 WRN] No store type was specified for the decimal property 'ExchangeRate' on entity type 'Currency'. This will cause values to be silently truncated if they do not fit in the default precision and scale. Explicitly specify the SQL server column type that can accommodate all the values in 'OnModelCreating' using 'HasColumnType', specify precision and scale using 'HasPrecision', or configure a value converter using 'HasConversion'.
[2025-07-11 00:08:29.586 +03:00 FTL] migration error
System.InvalidOperationException: An error was generated for warning 'Microsoft.EntityFrameworkCore.Migrations.PendingModelChangesWarning': The model for context 'ApplicationDbContext' has pending changes. Add a new migration before updating the database. See https://aka.ms/efcore-docs-pending-changes. This exception can be suppressed or logged by passing event ID 'RelationalEventId.PendingModelChangesWarning' to the 'ConfigureWarnings' method in 'DbContext.OnConfiguring' or 'AddDbContext'.
   at Microsoft.EntityFrameworkCore.Diagnostics.EventDefinition`1.Log[TLoggerCategory](IDiagnosticsLogger`1 logger, TParam arg)
   at Microsoft.EntityFrameworkCore.Diagnostics.RelationalLoggerExtensions.PendingModelChangesWarning(IDiagnosticsLogger`1 diagnostics, Type contextType)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.ValidateMigrations(Boolean useTransaction, String targetMigration)
   at Microsoft.EntityFrameworkCore.Migrations.Internal.Migrator.MigrateAsync(String targetMigration, CancellationToken cancellationToken)
   at Program.<Main>$(String[] args) in F:\torism and travel\TravelTourism.API\Program.cs:line 233
[2025-07-11 00:08:30.068 +03:00 INF] Starting Travel & Tourism API
